-- Test Object Creator - Erstellt einfache Test-Objekte
-- Testet ob Objekte in Studio sichtbar bleiben

print("🧪 Test Object Creator wird gestartet...")

-- Services
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Warte bis TestObjects verfügbar ist
local TestObjects = ReplicatedStorage:WaitForChild("Shared"):WaitForChild("TestObjects")
TestObjects = require(TestObjects)

-- Kurz warten bis alles geladen ist
wait(2)

print("🧪 Erstelle Test-Objekte für Persistenz-Test...")

-- Test-Objekte erstellen
TestObjects.CreateAllTestObjects()

print("✅ Test-Objekte erstellt!")
print("🔍 Prüfen Sie den Workspace in Studio:")
print("   1. Schauen Sie in den Workspace-Explorer")
print("   2. Sie sollten sehen:")
print("      - TestPlattform (blau<PERSON> Rechteck)")
print("      - TestRechteck (rotes Rechteck)")
print("      - TestBaumstamm (brauner Zylinder)")
print("      - TestBaumkrone (grüne Kugel)")
print("🎯 WICHTIGER TEST:")
print("   - Stoppen Sie das Spiel")
print("   - Schauen Sie ob die Objekte noch da sind")
print("   - Falls JA: Persistenz funktioniert!")
print("   - Falls NEIN: Wir brauchen andere Lösung")
