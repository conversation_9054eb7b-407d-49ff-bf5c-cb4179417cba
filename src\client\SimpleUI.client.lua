-- Einfache UI - Garantiert sichtbare Buttons
-- <PERSON><PERSON> erstellt sofort sichtbare GUI-Elemente

print("🎮 SimpleUI wird gestartet...")

-- Services
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")

-- Lokale Variablen
local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- <PERSON><PERSON> kurz, damit alles geladen ist
wait(2)

print("🎮 Erstelle einfache UI-Buttons...")

-- Erstelle ScreenGui
local screenGui = Instance.new("ScreenGui")
screenGui.Name = "SimpleGameUI"
screenGui.ResetOnSpawn = false
screenGui.Parent = playerGui

print("🎮 ScreenGui erstellt:", screenGui.Name)

-- <PERSON><PERSON><PERSON>uptframe für Buttons
local buttonFrame = Instance.new("Frame")
buttonFrame.Name = "ButtonFrame"
buttonFrame.Size = UDim2.new(0, 400, 0, 80)
buttonFrame.Position = UDim2.new(0.5, -200, 1, -100) -- Unten zentriert
buttonFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
buttonFrame.BackgroundTransparency = 0.2
buttonFrame.BorderSizePixel = 2
buttonFrame.BorderColor3 = Color3.fromRGB(255, 255, 255)
buttonFrame.Parent = screenGui

print("🎮 ButtonFrame erstellt")

-- Inventar-Button
local inventoryButton = Instance.new("TextButton")
inventoryButton.Name = "InventoryButton"
inventoryButton.Size = UDim2.new(0, 120, 0, 60)
inventoryButton.Position = UDim2.new(0, 10, 0, 10)
inventoryButton.BackgroundColor3 = Color3.fromRGB(100, 150, 200)
inventoryButton.Text = "🎒 INVENTAR"
inventoryButton.TextColor3 = Color3.fromRGB(255, 255, 255)
inventoryButton.TextScaled = true
inventoryButton.Font = Enum.Font.SourceSansBold
inventoryButton.Parent = buttonFrame

-- Shop-Button
local shopButton = Instance.new("TextButton")
shopButton.Name = "ShopButton"
shopButton.Size = UDim2.new(0, 120, 0, 60)
shopButton.Position = UDim2.new(0, 140, 0, 10)
shopButton.BackgroundColor3 = Color3.fromRGB(150, 100, 200)
shopButton.Text = "🛒 SHOP"
shopButton.TextColor3 = Color3.fromRGB(255, 255, 255)
shopButton.TextScaled = true
shopButton.Font = Enum.Font.SourceSansBold
shopButton.Parent = buttonFrame

-- Quest-Button
local questButton = Instance.new("TextButton")
questButton.Name = "QuestButton"
questButton.Size = UDim2.new(0, 120, 0, 60)
questButton.Position = UDim2.new(0, 270, 0, 10)
questButton.BackgroundColor3 = Color3.fromRGB(100, 200, 150)
questButton.Text = "📋 QUESTS"
questButton.TextColor3 = Color3.fromRGB(255, 255, 255)
questButton.TextScaled = true
questButton.Font = Enum.Font.SourceSansBold
questButton.Parent = buttonFrame

print("🎮 Alle Buttons erstellt!")

-- Variable um zu verfolgen, welches Fenster gerade offen ist
local currentOpenWindow = nil

-- Funktion zum Schließen aller offenen Fenster
local function closeAllWindows()
    local windowsToClose = {"SimpleInventory", "SimpleShop", "SimpleQuests"}

    for _, windowName in ipairs(windowsToClose) do
        local existingWindow = playerGui:FindFirstChild(windowName)
        if existingWindow then
            existingWindow:Destroy()
            print("🔄 " .. windowName .. " automatisch geschlossen")
        end
    end
    currentOpenWindow = nil
end

-- Einfache Inventar-Funktion
local function openInventory()
    print("🎒 Inventar-Button geklickt!")

    -- Wenn Inventar bereits offen ist, schließe nur dieses
    if currentOpenWindow == "SimpleInventory" then
        closeAllWindows()
        return
    end

    -- Schließe alle anderen Fenster zuerst
    closeAllWindows()

    -- Kurze Pause für saubere Animation
    wait(0.1)

    -- Öffne Inventar
    currentOpenWindow = "SimpleInventory"
    
    -- Erstelle einfaches Inventar-Fenster
    local inventoryGui = Instance.new("ScreenGui")
    inventoryGui.Name = "SimpleInventory"
    inventoryGui.Parent = playerGui
    
    local inventoryFrame = Instance.new("Frame")
    inventoryFrame.Size = UDim2.new(0, 500, 0, 400)
    inventoryFrame.Position = UDim2.new(0.5, -250, 0.5, -200)
    inventoryFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    inventoryFrame.BorderSizePixel = 3
    inventoryFrame.BorderColor3 = Color3.fromRGB(100, 150, 200)
    inventoryFrame.Parent = inventoryGui
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundColor3 = Color3.fromRGB(100, 150, 200)
    title.Text = "🎒 INVENTAR"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = inventoryFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -45, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "X"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = inventoryFrame
    
    closeButton.MouseButton1Click:Connect(function()
        inventoryGui:Destroy()
        currentOpenWindow = nil
        print("🎒 Inventar geschlossen")
    end)
    
    -- Inventar-Inhalt
    local content = Instance.new("TextLabel")
    content.Size = UDim2.new(1, -20, 1, -70)
    content.Position = UDim2.new(0, 10, 0, 60)
    content.BackgroundTransparency = 1
    content.Text = "🗡️ Basis Schwert x1\n🧪 Heiltrank x5\n🪵 Holz x10\n⚔️ Eisenschwert x1\n\n✨ Inventar funktioniert!"
    content.TextColor3 = Color3.fromRGB(255, 255, 255)
    content.TextScaled = true
    content.Font = Enum.Font.SourceSans
    content.TextXAlignment = Enum.TextXAlignment.Left
    content.TextYAlignment = Enum.TextYAlignment.Top
    content.Parent = inventoryFrame
    
    print("🎒 Inventar geöffnet!")
end

-- Einfache Shop-Funktion
local function openShop()
    print("🛒 Shop-Button geklickt!")

    -- Wenn Shop bereits offen ist, schließe nur diesen
    if currentOpenWindow == "SimpleShop" then
        closeAllWindows()
        return
    end

    -- Schließe alle anderen Fenster zuerst
    closeAllWindows()

    -- Kurze Pause für saubere Animation
    wait(0.1)

    -- Öffne Shop
    currentOpenWindow = "SimpleShop"
    
    local shopGui = Instance.new("ScreenGui")
    shopGui.Name = "SimpleShop"
    shopGui.Parent = playerGui
    
    local shopFrame = Instance.new("Frame")
    shopFrame.Size = UDim2.new(0, 500, 0, 400)
    shopFrame.Position = UDim2.new(0.5, -250, 0.5, -200)
    shopFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    shopFrame.BorderSizePixel = 3
    shopFrame.BorderColor3 = Color3.fromRGB(150, 100, 200)
    shopFrame.Parent = shopGui
    
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundColor3 = Color3.fromRGB(150, 100, 200)
    title.Text = "🛒 SHOP"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = shopFrame
    
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -45, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "X"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = shopFrame
    
    closeButton.MouseButton1Click:Connect(function()
        shopGui:Destroy()
        currentOpenWindow = nil
        print("🛒 Shop geschlossen")
    end)
    
    local content = Instance.new("TextLabel")
    content.Size = UDim2.new(1, -20, 1, -70)
    content.Position = UDim2.new(0, 10, 0, 60)
    content.BackgroundTransparency = 1
    content.Text = "💰 Verfügbare Items:\n\n🗡️ Schwert - 100 Coins\n🛡️ Rüstung - 150 Coins\n🧪 Trank - 25 Coins\n💎 Edelstein - 500 Coins\n\n💵 Deine Coins: 100"
    content.TextColor3 = Color3.fromRGB(255, 255, 255)
    content.TextScaled = true
    content.Font = Enum.Font.SourceSans
    content.TextXAlignment = Enum.TextXAlignment.Left
    content.TextYAlignment = Enum.TextYAlignment.Top
    content.Parent = shopFrame
    
    print("🛒 Shop geöffnet!")
end

-- Einfache Quest-Funktion
local function openQuests()
    print("📋 Quest-Button geklickt!")

    -- Wenn Quests bereits offen sind, schließe nur diese
    if currentOpenWindow == "SimpleQuests" then
        closeAllWindows()
        return
    end

    -- Schließe alle anderen Fenster zuerst
    closeAllWindows()

    -- Kurze Pause für saubere Animation
    wait(0.1)

    -- Öffne Quests
    currentOpenWindow = "SimpleQuests"
    
    local questGui = Instance.new("ScreenGui")
    questGui.Name = "SimpleQuests"
    questGui.Parent = playerGui
    
    local questFrame = Instance.new("Frame")
    questFrame.Size = UDim2.new(0, 500, 0, 400)
    questFrame.Position = UDim2.new(0.5, -250, 0.5, -200)
    questFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    questFrame.BorderSizePixel = 3
    questFrame.BorderColor3 = Color3.fromRGB(100, 200, 150)
    questFrame.Parent = questGui
    
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundColor3 = Color3.fromRGB(100, 200, 150)
    title.Text = "📋 QUESTS"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = questFrame
    
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -45, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "X"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = questFrame
    
    closeButton.MouseButton1Click:Connect(function()
        questGui:Destroy()
        currentOpenWindow = nil
        print("📋 Quests geschlossen")
    end)
    
    local content = Instance.new("TextLabel")
    content.Size = UDim2.new(1, -20, 1, -70)
    content.Position = UDim2.new(0, 10, 0, 60)
    content.BackgroundTransparency = 1
    content.Text = "🎯 Aktive Quests:\n\n✅ Willkommen! - Abgeschlossen\n🔄 Sammle 10 Holz (7/10)\n🔄 Überlebe 5 Minuten (3/5)\n🆕 Besuche den Shop (0/1)\n\n🏆 Belohnungen: Coins, XP, Items"
    content.TextColor3 = Color3.fromRGB(255, 255, 255)
    content.TextScaled = true
    content.Font = Enum.Font.SourceSans
    content.TextXAlignment = Enum.TextXAlignment.Left
    content.TextYAlignment = Enum.TextYAlignment.Top
    content.Parent = questFrame
    
    print("📋 Quests geöffnet!")
end

-- Button-Events verbinden
inventoryButton.MouseButton1Click:Connect(openInventory)
shopButton.MouseButton1Click:Connect(openShop)
questButton.MouseButton1Click:Connect(openQuests)

-- Tastatur-Shortcuts (P und Q)
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.P then
        openShop()
    elseif input.KeyCode == Enum.KeyCode.Q then
        openQuests()
    end
end)

print("✅ SimpleUI vollständig geladen!")
print("🎮 Buttons sollten jetzt am unteren Bildschirmrand sichtbar sein!")
print("🎮 Klicke auf die Buttons oder drücke P (Shop) / Q (Quests)")
