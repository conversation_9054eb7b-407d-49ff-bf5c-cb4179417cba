-- Map Generator - Erstellt automatisch eine Spielwelt
-- <PERSON><PERSON><PERSON><PERSON> auf dem Server und erstellt die Map für alle Spieler

print("🗺️ Map Generator wird gestartet...")

-- Services
local Workspace = game:GetService("Workspace")
local Lighting = game:GetService("Lighting")
local TweenService = game:GetService("TweenService")

-- Map-Konfiguration
local MAP_CONFIG = {
    -- Grundfläche
    GROUND_SIZE = 200,
    GROUND_HEIGHT = 2,
    
    -- Bereiche
    SPAWN_AREA_SIZE = 20,
    SHOP_AREA_SIZE = 15,
    QUEST_AREA_SIZE = 15,
    FOREST_AREA_SIZE = 30,
    MINING_AREA_SIZE = 25,
    
    -- Farben
    COLORS = {
        GROUND = Color3.fromRGB(34, 139, 34),      -- Gras-Grün
        SPAWN = Color3.fromRGB(100, 150, 200),     -- Blau
        SHOP = Color3.fromRGB(150, 100, 200),      -- <PERSON>
        QUEST = Color3.fromRGB(100, 200, 150),     -- <PERSON><PERSON><PERSON><PERSON>
        FOREST = Color3.fromRGB(0, 100, 0),        -- <PERSON><PERSON><PERSON>g<PERSON>ün
        MINING = Color3.fromRGB(139, 69, 19),      -- Braun
        WATER = Color3.fromRGB(0, 100, 255),       -- Blau
        STONE = Color3.fromRGB(128, 128, 128)      -- Grau
    }
}

-- Hilfsfunktion: Teil erstellen
local function createPart(name, size, position, color, material)
    local part = Instance.new("Part")
    part.Name = name
    part.Size = size
    part.Position = position
    part.Color = color
    part.Material = material or Enum.Material.Grass
    part.Anchored = true
    part.Parent = Workspace
    return part
end

-- Hilfsfunktion: Text-Label erstellen
local function createAreaLabel(text, position, color)
    local part = createPart("AreaLabel", Vector3.new(10, 1, 10), position, color, Enum.Material.Neon)
    
    local surfaceGui = Instance.new("SurfaceGui")
    surfaceGui.Face = Enum.NormalId.Top
    surfaceGui.Parent = part
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = text
    textLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.Parent = surfaceGui
    
    return part
end

-- Grundfläche erstellen
local function createGround()
    print("🗺️ Erstelle Grundfläche...")
    
    local ground = createPart(
        "Ground",
        Vector3.new(MAP_CONFIG.GROUND_SIZE, MAP_CONFIG.GROUND_HEIGHT, MAP_CONFIG.GROUND_SIZE),
        Vector3.new(0, 0, 0),
        MAP_CONFIG.COLORS.GROUND,
        Enum.Material.Grass
    )
    
    print("✅ Grundfläche erstellt")
    return ground
end

-- Spawn-Bereich erstellen
local function createSpawnArea()
    print("🗺️ Erstelle Spawn-Bereich...")
    
    local spawnPlatform = createPart(
        "SpawnArea",
        Vector3.new(MAP_CONFIG.SPAWN_AREA_SIZE, 3, MAP_CONFIG.SPAWN_AREA_SIZE),
        Vector3.new(0, 3, 0),
        MAP_CONFIG.COLORS.SPAWN,
        Enum.Material.Neon
    )
    
    -- Spawn-Label
    createAreaLabel("🏠 SPAWN", Vector3.new(0, 7, 0), MAP_CONFIG.COLORS.SPAWN)
    
    -- Spawn-Point setzen
    local spawnLocation = Instance.new("SpawnLocation")
    spawnLocation.Size = Vector3.new(6, 1, 6)
    spawnLocation.Position = Vector3.new(0, 5, 0)
    spawnLocation.BrickColor = BrickColor.new("Bright blue")
    spawnLocation.Material = Enum.Material.Neon
    spawnLocation.Anchored = true
    spawnLocation.Parent = Workspace
    
    print("✅ Spawn-Bereich erstellt")
end

-- Shop-Bereich erstellen
local function createShopArea()
    print("🗺️ Erstelle Shop-Bereich...")
    
    local shopPlatform = createPart(
        "ShopArea",
        Vector3.new(MAP_CONFIG.SHOP_AREA_SIZE, 2, MAP_CONFIG.SHOP_AREA_SIZE),
        Vector3.new(-40, 2, -40),
        MAP_CONFIG.COLORS.SHOP,
        Enum.Material.Marble
    )
    
    -- Shop-Label
    createAreaLabel("🛒 SHOP", Vector3.new(-40, 5, -40), MAP_CONFIG.COLORS.SHOP)
    
    -- Shop-Gebäude
    local shopBuilding = createPart(
        "ShopBuilding",
        Vector3.new(12, 8, 12),
        Vector3.new(-40, 6, -40),
        MAP_CONFIG.COLORS.SHOP,
        Enum.Material.Brick
    )
    
    print("✅ Shop-Bereich erstellt")
end

-- Quest-Bereich erstellen
local function createQuestArea()
    print("🗺️ Erstelle Quest-Bereich...")
    
    local questPlatform = createPart(
        "QuestArea",
        Vector3.new(MAP_CONFIG.QUEST_AREA_SIZE, 2, MAP_CONFIG.QUEST_AREA_SIZE),
        Vector3.new(40, 2, -40),
        MAP_CONFIG.COLORS.QUEST,
        Enum.Material.Marble
    )
    
    -- Quest-Label
    createAreaLabel("📋 QUESTS", Vector3.new(40, 5, -40), MAP_CONFIG.COLORS.QUEST)
    
    -- Quest-Gebäude
    local questBuilding = createPart(
        "QuestBuilding",
        Vector3.new(12, 8, 12),
        Vector3.new(40, 6, -40),
        MAP_CONFIG.COLORS.QUEST,
        Enum.Material.Brick
    )
    
    print("✅ Quest-Bereich erstellt")
end

-- Wald-Bereich erstellen
local function createForestArea()
    print("🗺️ Erstelle Wald-Bereich...")
    
    local forestGround = createPart(
        "ForestArea",
        Vector3.new(MAP_CONFIG.FOREST_AREA_SIZE, 1, MAP_CONFIG.FOREST_AREA_SIZE),
        Vector3.new(-60, 1, 40),
        MAP_CONFIG.COLORS.FOREST,
        Enum.Material.Grass
    )
    
    -- Wald-Label
    createAreaLabel("🌲 WALD", Vector3.new(-60, 3, 40), MAP_CONFIG.COLORS.FOREST)
    
    -- Bäume erstellen
    for i = 1, 8 do
        local x = -60 + math.random(-10, 10)
        local z = 40 + math.random(-10, 10)
        
        -- Baumstamm
        local trunk = createPart(
            "TreeTrunk",
            Vector3.new(2, 8, 2),
            Vector3.new(x, 5, z),
            Color3.fromRGB(101, 67, 33),
            Enum.Material.Wood
        )
        
        -- Baumkrone
        local leaves = createPart(
            "TreeLeaves",
            Vector3.new(6, 6, 6),
            Vector3.new(x, 11, z),
            Color3.fromRGB(0, 128, 0),
            Enum.Material.Grass
        )
        leaves.Shape = Enum.PartType.Ball
    end
    
    print("✅ Wald-Bereich erstellt")
end

-- Mining-Bereich erstellen
local function createMiningArea()
    print("🗺️ Erstelle Mining-Bereich...")
    
    local miningGround = createPart(
        "MiningArea",
        Vector3.new(MAP_CONFIG.MINING_AREA_SIZE, 1, MAP_CONFIG.MINING_AREA_SIZE),
        Vector3.new(60, 1, 40),
        MAP_CONFIG.COLORS.MINING,
        Enum.Material.Rock
    )
    
    -- Mining-Label
    createAreaLabel("⛏️ MINE", Vector3.new(60, 3, 40), MAP_CONFIG.COLORS.MINING)
    
    -- Felsen und Erze erstellen
    for i = 1, 6 do
        local x = 60 + math.random(-8, 8)
        local z = 40 + math.random(-8, 8)
        
        -- Felsen
        local rock = createPart(
            "Rock",
            Vector3.new(math.random(3, 6), math.random(3, 6), math.random(3, 6)),
            Vector3.new(x, 4, z),
            MAP_CONFIG.COLORS.STONE,
            Enum.Material.Rock
        )
        
        -- Zufällige Erze
        if math.random() > 0.5 then
            local ore = createPart(
                "Ore",
                Vector3.new(2, 2, 2),
                Vector3.new(x + 2, 3, z + 2),
                Color3.fromRGB(255, 215, 0), -- Gold
                Enum.Material.Neon
            )
        end
    end
    
    print("✅ Mining-Bereich erstellt")
end

-- Wasser-Bereich erstellen
local function createWaterArea()
    print("🗺️ Erstelle Wasser-Bereich...")
    
    local water = createPart(
        "WaterArea",
        Vector3.new(40, 8, 40),
        Vector3.new(0, -2, 60),
        MAP_CONFIG.COLORS.WATER,
        Enum.Material.ForceField
    )
    water.Transparency = 0.5
    water.CanCollide = false
    
    -- Wasser-Label
    createAreaLabel("🌊 WASSER", Vector3.new(0, 3, 60), MAP_CONFIG.COLORS.WATER)
    
    print("✅ Wasser-Bereich erstellt")
end

-- Beleuchtung einrichten
local function setupLighting()
    print("🗺️ Richte Beleuchtung ein...")
    
    Lighting.Brightness = 2
    Lighting.Ambient = Color3.fromRGB(100, 100, 100)
    Lighting.TimeOfDay = "12:00:00"
    Lighting.FogEnd = 1000
    
    -- Skybox (optional)
    local sky = Instance.new("Sky")
    sky.SkyboxBk = "rbxasset://textures/sky/sky512_bk.tex"
    sky.SkyboxDn = "rbxasset://textures/sky/sky512_dn.tex"
    sky.SkyboxFt = "rbxasset://textures/sky/sky512_ft.tex"
    sky.SkyboxLf = "rbxasset://textures/sky/sky512_lf.tex"
    sky.SkyboxRt = "rbxasset://textures/sky/sky512_rt.tex"
    sky.SkyboxUp = "rbxasset://textures/sky/sky512_up.tex"
    sky.Parent = Lighting
    
    print("✅ Beleuchtung eingerichtet")
end

-- Hauptfunktion: Map erstellen
local function generateMap()
    print("🗺️ Starte Map-Generierung...")
    
    -- Lösche alte Map-Elemente
    for _, obj in pairs(Workspace:GetChildren()) do
        if obj.Name:find("Area") or obj.Name == "Ground" or obj.Name:find("Tree") or obj.Name:find("Rock") or obj.Name:find("Building") then
            obj:Destroy()
        end
    end
    
    -- Erstelle Map-Bereiche
    createGround()
    createSpawnArea()
    createShopArea()
    createQuestArea()
    createForestArea()
    createMiningArea()
    createWaterArea()
    setupLighting()
    
    print("✅ Map-Generierung abgeschlossen!")
    print("🗺️ Bereiche erstellt:")
    print("   🏠 Spawn (Mitte)")
    print("   🛒 Shop (Links)")
    print("   📋 Quests (Rechts)")
    print("   🌲 Wald (Hinten Links)")
    print("   ⛏️ Mine (Hinten Rechts)")
    print("   🌊 Wasser (Hinten Mitte)")
end

-- Map generieren
wait(2) -- Kurz warten bis Server bereit ist
generateMap()

print("🎮 Map ist bereit für Spieler!")
