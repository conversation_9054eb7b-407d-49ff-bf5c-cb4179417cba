# 🗺️ Manuelle Map-Erstellung für Roblox Studio

Falls die automatische Map-Erstellung nicht funktioniert, können Sie die Map manuell erstellen.

## 🎯 Schritt-für-Schritt Anleitung:

### 1. Workspace vorbereiten
1. Öffnen Sie Roblox Studio
2. Erstellen Sie einen neuen Ordner im Workspace namens `PermanentGameMap`

### 2. Grundboden erstellen
1. Fügen Sie ein Part hinzu
2. Benennen Sie es `MainGround`
3. Setzen Sie die Eigenschaften:
   - Size: `400, 3, 400`
   - Position: `0, 0, 0`
   - Color: `76, 153, 0` (RGB)
   - Material: `Grass`
   - Anchored: `true`

### 3. Spawn-Bereich erstellen
1. Erstellen Sie einen Ordner `SpawnArea` in `PermanentGameMap`
2. Fügen Sie ein Part hinzu: `SpawnPlatform`
   - Size: `25, 1, 25`
   - Position: `0, 2, 0`
   - Color: `65, 131, 215` (RGB)
   - Material: `Marble`
   - Anchored: `true`
3. Fügen Sie ein SpawnLocation hinzu: `MainSpawn`
   - Size: `4, 0.2, 4`
   - Position: `0, 4, 0`
   - Transparency: `0.5`
   - BrickColor: `Bright blue`
   - Material: `ForceField`
   - Anchored: `true`

### 4. Shop-Bereich erstellen
1. Erstellen Sie einen Ordner `ShopArea` in `PermanentGameMap`
2. Fügen Sie Parts hinzu:
   - `ShopPlatform`: Size `30, 2, 30`, Position `-80, 3, -80`, Color `186, 85, 211`
   - `ShopBuilding`: Size `20, 15, 20`, Position `-80, 11, -80`, Material `Brick`
   - `ShopRoof`: Size `22, 3, 22`, Position `-80, 19, -80`, Color `139, 69, 19`, Material `Wood`

### 5. Quest-Bereich erstellen
1. Erstellen Sie einen Ordner `QuestArea` in `PermanentGameMap`
2. Fügen Sie Parts hinzu:
   - `QuestPlatform`: Size `30, 2, 30`, Position `80, 3, -80`, Color `50, 205, 50`
   - `QuestBuilding`: Size `20, 15, 20`, Position `80, 11, -80`, Material `Brick`
   - `QuestRoof`: Size `22, 3, 22`, Position `80, 19, -80`, Color `139, 69, 19`, Material `Wood`

### 6. Wald-Bereich erstellen
1. Erstellen Sie einen Ordner `ForestArea` in `PermanentGameMap`
2. Fügen Sie hinzu:
   - `ForestGround`: Size `60, 1, 60`, Position `-100, 2, 80`, Color `34, 139, 34`, Material `Grass`
3. Erstellen Sie 5-10 Bäume:
   - Stamm: Size `2, 12, 2`, Material `Wood`, Color `101, 67, 33`
   - Krone: Size `8, 8, 8`, Shape `Ball`, Material `Grass`, Color `0, 128, 0`

### 7. Mining-Bereich erstellen
1. Erstellen Sie einen Ordner `MiningArea` in `PermanentGameMap`
2. Fügen Sie hinzu:
   - `MiningGround`: Size `50, 1, 50`, Position `100, 2, 80`, Color `160, 82, 45`, Material `Rock`
3. Erstellen Sie 5-8 Felsen:
   - Size `5, 5, 5`, Material `Rock`, Color `105, 105, 105`
4. Fügen Sie Erze hinzu:
   - Size `2, 2, 2`, Material `Neon`, Color `255, 215, 0` (Gold)

### 8. Wasser-Bereich erstellen
1. Erstellen Sie einen Ordner `WaterArea` in `PermanentGameMap`
2. Fügen Sie hinzu:
   - `WaterArea`: Size `80, 12, 80`, Position `0, -3, 120`, Color `30, 144, 255`, Material `ForceField`, Transparency `0.6`, CanCollide `false`
   - `CenterIsland`: Size `12, 2, 12`, Position `0, 1, 120`, Material `Grass`

### 9. Labels erstellen (Optional)
Für jeden Bereich können Sie Labels erstellen:
1. Fügen Sie ein Part hinzu (Size `12, 1, 12`, Material `Neon`, Transparency `0.3`)
2. Fügen Sie eine SurfaceGui hinzu (Face: `Top`)
3. Fügen Sie ein TextLabel hinzu mit dem Bereichsnamen

### 10. Beleuchtung einstellen
1. Gehen Sie zu Lighting
2. Setzen Sie:
   - Brightness: `2.5`
   - Ambient: `120, 120, 120` (RGB)
   - TimeOfDay: `14:00:00`
   - FogEnd: `2000`

## 🎮 Fertig!

Nach diesen Schritten haben Sie eine permanente Map, die auch nach dem Schließen von Studio erhalten bleibt.

## 📁 Ordnerstruktur:
```
Workspace/
└── PermanentGameMap/
    ├── MainGround
    ├── SpawnArea/
    ├── ShopArea/
    ├── QuestArea/
    ├── ForestArea/
    ├── MiningArea/
    └── WaterArea/
```

## 💡 Tipps:
- Verwenden Sie Copy & Paste für ähnliche Objekte
- Gruppieren Sie verwandte Objekte in Ordnern
- Speichern Sie regelmäßig Ihr Studio-Projekt
- Testen Sie die Map im Play-Modus

## 🔧 Fehlerbehebung:
- Falls Spawn nicht funktioniert: Prüfen Sie Position und Anchored-Eigenschaft
- Falls Objekte verschwinden: Stellen Sie sicher, dass sie im Workspace und nicht in einem Service sind
- Falls Performance-Probleme: Reduzieren Sie die Anzahl der Objekte

Diese manuelle Methode garantiert, dass Ihre Map dauerhaft erhalten bleibt!
