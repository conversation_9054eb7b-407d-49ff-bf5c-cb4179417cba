-- EINFACHE GUI BUTTONS - Ko<PERSON>ren Sie diesen Code in Roblox Studio
-- 1. Öffnen Sie Roblox Studio
-- 2. <PERSON><PERSON><PERSON> Si<PERSON> zu StarterPlayerScripts (oder StarterGui)
-- 3. <PERSON><PERSON>sklick → Insert Object → LocalScript
-- 4. <PERSON><PERSON><PERSON>e DIESEN GESAMTEN CODE hinein
-- 5. <PERSON><PERSON><PERSON> Sie Strg+S zum Speichern
-- 6. Die Buttons werden sofort erstellt!

print("🎮 EINFACHE GUI BUTTONS WERDEN ERSTELLT...")

-- Services
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

-- Lokale Variablen
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- <PERSON>te kurz bis alles geladen ist
wait(2)

-- Variable um zu verfolgen, welches Fenster offen ist
local currentOpenWindow = nil

-- Funktion zum Schließen aller Fenster
local function closeAllWindows()
    local windowsToClose = {"InventoryGUI", "ShopGUI", "QuestGUI"}
    
    for _, windowName in ipairs(windowsToClose) do
        local existingWindow = playerGui:FindFirstChild(windowName)
        if existingWindow then
            existingWindow:Destroy()
            print("🔄 " .. windowName .. " geschlossen")
        end
    end
    currentOpenWindow = nil
end

-- Hilfsfunktion: Button erstellen
local function createButton(name, text, icon, color, position)
    local button = Instance.new("TextButton")
    button.Name = name
    button.Size = UDim2.new(0, 100, 0, 60)
    button.Position = position
    button.BackgroundColor3 = color
    button.BorderSizePixel = 2
    button.BorderColor3 = Color3.fromRGB(255, 255, 255)
    button.Text = icon .. "\n" .. text
    button.TextColor3 = Color3.fromRGB(255, 255, 255)
    button.TextScaled = true
    button.Font = Enum.Font.SourceSansBold
    
    -- Abgerundete Ecken
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = button
    
    -- Hover-Effekt
    button.MouseEnter:Connect(function()
        local tween = TweenService:Create(button, TweenInfo.new(0.2), {
            Size = UDim2.new(0, 110, 0, 65),
            BackgroundTransparency = 0.1
        })
        tween:Play()
    end)
    
    button.MouseLeave:Connect(function()
        local tween = TweenService:Create(button, TweenInfo.new(0.2), {
            Size = UDim2.new(0, 100, 0, 60),
            BackgroundTransparency = 0
        })
        tween:Play()
    end)
    
    return button
end

-- Hilfsfunktion: Fenster erstellen
local function createWindow(name, title, color, content)
    -- Schließe alle anderen Fenster
    closeAllWindows()
    
    -- Kurze Pause
    wait(0.1)
    
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = name
    screenGui.Parent = playerGui
    
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 500, 0, 400)
    mainFrame.Position = UDim2.new(0.5, -250, 0.5, -200)
    mainFrame.BackgroundColor3 = color
    mainFrame.BorderSizePixel = 3
    mainFrame.BorderColor3 = Color3.fromRGB(255, 255, 255)
    mainFrame.Parent = screenGui
    
    -- Abgerundete Ecken
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 12)
    corner.Parent = mainFrame
    
    -- Titel
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 50)
    titleLabel.BackgroundColor3 = Color3.new(0, 0, 0)
    titleLabel.BackgroundTransparency = 0.3
    titleLabel.Text = title
    titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = mainFrame
    
    -- Titel-Ecken
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 12)
    titleCorner.Parent = titleLabel
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -45, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 8)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        screenGui:Destroy()
        currentOpenWindow = nil
        print("🔄 " .. name .. " geschlossen")
    end)
    
    -- Inhalt
    local contentLabel = Instance.new("TextLabel")
    contentLabel.Size = UDim2.new(1, -20, 1, -70)
    contentLabel.Position = UDim2.new(0, 10, 0, 60)
    contentLabel.BackgroundTransparency = 1
    contentLabel.Text = content
    contentLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    contentLabel.TextScaled = true
    contentLabel.Font = Enum.Font.SourceSans
    contentLabel.TextXAlignment = Enum.TextXAlignment.Left
    contentLabel.TextYAlignment = Enum.TextYAlignment.Top
    contentLabel.Parent = mainFrame
    
    currentOpenWindow = name
    print("✅ " .. name .. " geöffnet")
    return screenGui
end

-- Inventar-Funktion
local function openInventory()
    if currentOpenWindow == "InventoryGUI" then
        closeAllWindows()
        return
    end
    
    createWindow(
        "InventoryGUI",
        "🎒 INVENTAR",
        Color3.fromRGB(100, 150, 200),
        "🎒 Dein Inventar:\n\n🗡️ Basis Schwert x1\n🧪 Heiltrank x5\n🪵 Holz x10\n⚔️ Eisenschwert x1\n💎 Edelstein x2\n\n✨ Inventar-System funktioniert!\n\nKlicke auf Items um sie zu verwenden."
    )
end

-- Shop-Funktion
local function openShop()
    if currentOpenWindow == "ShopGUI" then
        closeAllWindows()
        return
    end
    
    createWindow(
        "ShopGUI",
        "🛒 SHOP",
        Color3.fromRGB(150, 100, 200),
        "🛒 Willkommen im Shop!\n\n💰 Verfügbare Items:\n\n🗡️ Schwert - 100 Coins\n🛡️ Rüstung - 150 Coins\n🧪 Heiltrank - 25 Coins\n💎 Edelstein - 500 Coins\n🏹 Bogen - 75 Coins\n\n💵 Deine Coins: 250\n\n💡 Klicke auf Items zum Kaufen!"
    )
end

-- Quest-Funktion
local function openQuests()
    if currentOpenWindow == "QuestGUI" then
        closeAllWindows()
        return
    end
    
    createWindow(
        "QuestGUI",
        "📋 QUESTS",
        Color3.fromRGB(100, 200, 150),
        "📋 Aktive Quests:\n\n✅ Willkommen! - Abgeschlossen (100 XP)\n🔄 Sammle 10 Holz (7/10) - 50 XP\n🔄 Überlebe 5 Minuten (3/5) - 75 XP\n🆕 Besuche den Shop (0/1) - 25 XP\n🆕 Besiege 5 Gegner (0/5) - 100 XP\n\n🏆 Belohnungen:\n💰 Coins, ⭐ XP, 🎁 Items\n\n📈 Level: 2 | XP: 150/300"
    )
end

-- Hauptfunktion: GUI erstellen
local function createMainGUI()
    print("🎮 Erstelle Haupt-GUI...")
    
    -- Lösche alte GUI falls vorhanden
    local oldGUI = playerGui:FindFirstChild("MainGameGUI")
    if oldGUI then
        oldGUI:Destroy()
    end
    
    -- Erstelle ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "MainGameGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe für Buttons
    local buttonFrame = Instance.new("Frame")
    buttonFrame.Name = "ButtonFrame"
    buttonFrame.Size = UDim2.new(0, 330, 0, 80)
    buttonFrame.Position = UDim2.new(0.5, -165, 1, -100) -- Unten zentriert
    buttonFrame.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
    buttonFrame.BackgroundTransparency = 0.2
    buttonFrame.BorderSizePixel = 2
    buttonFrame.BorderColor3 = Color3.fromRGB(255, 255, 255)
    buttonFrame.Parent = screenGui
    
    -- Abgerundete Ecken für Hauptframe
    local frameCorner = Instance.new("UICorner")
    frameCorner.CornerRadius = UDim.new(0, 12)
    frameCorner.Parent = buttonFrame
    
    -- Inventar-Button
    local inventoryButton = createButton(
        "InventoryButton",
        "INVENTAR",
        "🎒",
        Color3.fromRGB(100, 150, 200),
        UDim2.new(0, 10, 0, 10)
    )
    inventoryButton.Parent = buttonFrame
    inventoryButton.MouseButton1Click:Connect(openInventory)
    
    -- Shop-Button
    local shopButton = createButton(
        "ShopButton",
        "SHOP",
        "🛒",
        Color3.fromRGB(150, 100, 200),
        UDim2.new(0, 115, 0, 10)
    )
    shopButton.Parent = buttonFrame
    shopButton.MouseButton1Click:Connect(openShop)
    
    -- Quest-Button
    local questButton = createButton(
        "QuestButton",
        "QUESTS",
        "📋",
        Color3.fromRGB(100, 200, 150),
        UDim2.new(0, 220, 0, 10)
    )
    questButton.Parent = buttonFrame
    questButton.MouseButton1Click:Connect(openQuests)
    
    print("✅ Haupt-GUI erstellt!")
end

-- Tastatur-Shortcuts (optional)
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.I then
        openInventory()
    elseif input.KeyCode == Enum.KeyCode.P then
        openShop()
    elseif input.KeyCode == Enum.KeyCode.Q then
        openQuests()
    elseif input.KeyCode == Enum.KeyCode.Escape then
        closeAllWindows()
    end
end)

-- GUI erstellen
createMainGUI()

print("🎉 GUI BUTTONS ERFOLGREICH ERSTELLT!")
print("🎮 Buttons sind am unteren Bildschirmrand sichtbar!")
print("⌨️ Tastatur-Shortcuts:")
print("   I = Inventar")
print("   P = Shop") 
print("   Q = Quests")
print("   ESC = Alle Fenster schließen")
print("💡 Klicke auf die Buttons oder nutze die Tasten!")
