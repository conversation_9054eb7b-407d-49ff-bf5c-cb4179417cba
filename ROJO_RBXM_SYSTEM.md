# 🎯 Rojo .rbxm System - Permanente Objekte in Studio

Das .rbxm System ermöglicht es, Ob<PERSON><PERSON><PERSON> dauerhaft in Roblox Studio zu importieren, ohne dass sie beim Stoppen verschwinden.

## 🔧 **System-Einrichtung (bereits erledigt):**

Die `default.project.json` wurde bereits konfiguriert:
```json
"Workspace": {
    "$className": "Workspace",
    "TestObjects": {
        "$path": "assets/TestObjects.rbxm"
    },
    "GameMap": {
        "$path": "assets/GameMap.rbxm"
    }
}
```

## 📋 **Schritt-für-Schritt Anleitung:**

### **Schritt 1: Test-Objekte in Studio erstellen**
1. **Öffnen Sie Roblox Studio**
2. **Erstellen Sie ein neues Place**
3. **Erstellen Sie folgende Objekte im Workspace:**

#### **Test-Rechteck:**
- **Insert → Part**
- **Name:** `TestRechteck`
- **Size:** `10, 5, 2`
- **Position:** `0, 10, 0`
- **Color:** `255, 100, 100` (Rot)
- **Material:** `Neon`
- **Anchored:** `✓ true`

#### **Test-Baum:**
**Baumstamm:**
- **Insert → Part**
- **Name:** `TestBaumstamm`
- **Size:** `2, 8, 2`
- **Position:** `10, 4, 0`
- **Color:** `101, 67, 33` (Braun)
- **Material:** `Wood`
- **Anchored:** `✓ true`

**Baumkrone:**
- **Insert → Part**
- **Name:** `TestBaumkrone`
- **Size:** `6, 6, 6`
- **Position:** `10, 10, 0`
- **Color:** `0, 128, 0` (Grün)
- **Material:** `Grass`
- **Shape:** `Ball`
- **Anchored:** `✓ true`

#### **Test-Plattform:**
- **Insert → Part**
- **Name:** `TestPlattform`
- **Size:** `20, 1, 20`
- **Position:** `0, 0, 0`
- **Color:** `100, 100, 255` (Blau)
- **Material:** `Marble`
- **Anchored:** `✓ true`

### **Schritt 2: Objekte als .rbxm exportieren**
1. **Erstellen Sie einen Ordner im Workspace:** `TestObjects`
2. **Verschieben Sie alle erstellten Objekte in diesen Ordner**
3. **Rechtsklick auf den TestObjects Ordner**
4. **"Save to File..." → Speichern als:** `TestObjects.rbxm`
5. **Speichern Sie in:** `C:\Users\<USER>\RobloxEntwicklung\assets\TestObjects.rbxm`

### **Schritt 3: Rojo-Sync testen**
1. **Stellen Sie sicher, dass Rojo läuft** (Terminal ID 1)
2. **Öffnen Sie Ihr Hauptprojekt in Studio**
3. **Rojo Plugin → Sync**
4. **Schauen Sie in den Workspace** → Sie sollten den `TestObjects` Ordner sehen

### **Schritt 4: Persistenz-Test**
1. **Stoppen Sie das Spiel** (falls es läuft)
2. **Schauen Sie in den Workspace**
3. **Sind die TestObjects noch da?** ✅ JA = Erfolg!

## 🎮 **Erweiterte Map-Erstellung:**

### **Für eine vollständige Map:**
1. **Erstellen Sie in Studio:**
   - Grundboden (großes grünes Rechteck)
   - Spawn-Bereich (blaue Plattform)
   - Shop-Gebäude (lila Gebäude)
   - Quest-Gebäude (grünes Gebäude)
   - Wald-Bereich (mehrere Bäume)
   - Mining-Bereich (Felsen und Erze)

2. **Organisieren Sie in Ordnern:**
   ```
   GameMap/
   ├── Terrain/
   ├── SpawnArea/
   ├── ShopArea/
   ├── QuestArea/
   ├── ForestArea/
   └── MiningArea/
   ```

3. **Exportieren als GameMap.rbxm**
4. **Speichern in:** `assets/GameMap.rbxm`

## 🔧 **Vorteile des .rbxm Systems:**

### ✅ **Garantierte Persistenz:**
- Objekte bleiben dauerhaft erhalten
- Keine Script-Abhängigkeiten
- Funktioniert auch ohne Play-Modus

### ✅ **Vollständige Kontrolle:**
- Sie erstellen alles visuell in Studio
- Sofortige Sichtbarkeit
- Einfache Bearbeitung

### ✅ **Professioneller Workflow:**
- Versionskontrolle für Map-Änderungen
- Einfaches Teilen von Map-Elementen
- Modulare Map-Entwicklung

## 🎯 **Nächste Schritte:**

1. **Testen Sie das TestObjects.rbxm System**
2. **Falls erfolgreich:** Erstellen Sie eine vollständige GameMap.rbxm
3. **GUI-Buttons:** Können wir auch als .rbxm erstellen

## 💡 **Tipps:**

- **Benennen Sie Objekte eindeutig** (für einfache Identifikation)
- **Verwenden Sie Ordner** für Organisation
- **Testen Sie regelmäßig** mit Rojo-Sync
- **Speichern Sie Backups** Ihrer .rbxm Dateien

## 🔍 **Fehlerbehebung:**

**Falls .rbxm nicht lädt:**
- Prüfen Sie den Dateipfad in `default.project.json`
- Stellen Sie sicher, dass die .rbxm Datei im `assets/` Ordner ist
- Versuchen Sie Rojo-Neustart

**Falls Objekte verschwinden:**
- Das .rbxm System sollte das verhindern
- Falls doch: Prüfen Sie die Rojo-Konfiguration

## 🎉 **Erwartetes Ergebnis:**

Nach erfolgreichem Setup haben Sie:
- **Permanente Test-Objekte** im Workspace
- **Funktionierendes .rbxm Import-System**
- **Basis für vollständige Map-Erstellung**
- **Lösung für alle Persistenz-Probleme**

Dieses System sollte definitiv funktionieren! 🚀
