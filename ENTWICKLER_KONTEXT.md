# 👨‍💻 Entwickler-Kontext für Thread-Übergabe

## 🎯 **Benutzer-Präferenzen:**
- **Sprache**: <PERSON>utsch (alle Kommunikation)
- **Automatisierung**: Vollständige Automation gewünscht
- **Git-Management**: AI übernimmt komplette Verantwortung für Workflow
- **Entwicklungsstil**: Professionelle Standards, modulare Architektur
- **Testing**: Automatisierte Tests bevorzugt

## 🔧 **Entwicklungsumgebung:**
- **OS**: Windows 10
- **IDE**: Visual Studio Code (vollständig konfiguriert)
- **Synchronisation**: Rojo v7.5.1 (läuft auf Port 34872)
- **Versionskontrolle**: Git mit GitHub-Integration
- **Repository**: https://github.com/imknowledge/multifunktionales-roblox-spiel

## 🎮 **Projekt-Ziele:**
- **Spieltyp**: Multifunktionales Multiplayer-Roblox-Spiel
- **Zielgruppe**: 6-60 Jahre
- **Spieleranzahl**: 12+ pro Server, 20+ Server-Gruppen
- **Plattformen**: PC & Mobile
- **Persistenz**: Spielerdaten über Server hinweg

## 🏗️ **Architektur-Prinzipien:**
- **Modulare Services**: Jeder Service hat spezifische Verantwortung
- **Event-driven**: Lose gekoppelte Kommunikation über EventBus
- **Client-Server Trennung**: Klare Separation of Concerns
- **Persistent Data**: DataStore für spielerübergreifende Daten
- **Scalable Design**: Erweiterbar für neue Features

## 🌿 **Git-Workflow (AI-verwaltet):**
- **Branch-Strategie**: GitFlow (main → develop → feature/*)
- **Commit-Konventionen**: Conventional Commits (feat:, fix:, docs:)
- **Merge-Strategie**: No-FF für klare Historie
- **Automatisierung**: AI übernimmt alle Git-Operationen

## 🧪 **Testing-Ansatz:**
- **Automatisierte Tests**: Logger-System, UI-Tests
- **Manual Testing**: In Roblox Studio Play-Modus
- **Debug-Tools**: Console-Logs, Test-UIs
- **Continuous Testing**: Bei jedem Feature-Commit

## 📁 **Wichtige Dateien zum Verstehen:**

### **Konfiguration:**
- `default.project.json` - Rojo-Projektstruktur
- `src/shared/Config.lua` - Globale Spieleinstellungen
- `.vscode/` - VS Code Workspace-Konfiguration

### **Kern-Module:**
- `src/modules/EventBus.lua` - Event-System
- `src/shared/Logger.lua` - Logging-Framework
- `src/services/PlayerDataService.lua` - Spielerdaten
- `src/services/InventoryService.lua` - Item-Management
- `src/services/EconomyService.lua` - Währung & Shop
- `src/services/QuestService.lua` - Quest-System mit 72h-Persistenz

### **UI-System:**
- `src/components/init.lua` - UI-Framework mit permanenter Button-Leiste
- `src/client/UITest.client.lua` - Test-Client (modernisiert, ohne I-Taste)

## 🔄 **Aktueller Arbeitsstand:**
- **Letztes Feature**: UI-Button-System implementiert (I-Taste Konflikt behoben)
- **Aktuelle Lösung**: Permanente UI-Button-Leiste mit mobiler Unterstützung
- **UI-Navigation**: Inventar, Shop und Quests über moderne Buttons zugänglich
- **Nächster Schritt**: Combat-System entwickeln

## 💡 **Entwicklungsphilosophie:**
- **Kleine Commits**: Vermeidung von Long-Thread-Problemen
- **Iterative Entwicklung**: Feature-by-Feature
- **Quality First**: Tests vor neuen Features
- **User-Centric**: Sofort testbare Funktionen

## 🎯 **Erfolgs-Metriken:**
- **Funktionalität**: Alle Features sofort testbar
- **Code-Qualität**: Modulare, wartbare Architektur
- **Git-Historie**: Professionelle Commit-Messages
- **Dokumentation**: Umfassend und aktuell

## 🚀 **Nächste Entwicklungsziele:**

### **Kurzfristig (1-2 Tage):**
1. Combat-System Basis implementieren
2. Quest-Progress-Bars hinzufügen
3. Erweiterte Item-Details in Inventory-UI

### **Mittelfristig (1 Woche):**
1. Combat-System entwickeln
2. Crafting-System hinzufügen
3. Mobile-Optimierung

### **Langfristig (2+ Wochen):**
1. Advanced Features (Guilds, PvP)
2. Performance-Optimierung
3. Release-Vorbereitung

## 🔧 **Bekannte Herausforderungen:**
- **UI-Integration**: Vollständige Event-Handler-Integration
- **Performance**: DataStore-Optimierung für viele Spieler
- **Cross-Platform**: Mobile-UI-Anpassungen
- **Skalierung**: Quest-System für große Spielerzahlen

## 📞 **Support-Informationen:**
- **GitHub-Credentials**: imknowledge / dev99Sols! / <EMAIL>
- **Rojo-Server**: Läuft auf localhost:34872
- **Test-Commands**: I (Inventar), P (Shop), Q (Quests)
- **Debug-Output**: Roblox Studio Output-Fenster
- **DataStore**: TempQuestData_v1 für 72h-Persistenz

---

**Für neuen Thread**: Kopiere PROJEKT_STATUS_AKTUELL.md + ENTWICKLER_KONTEXT.md
**Arbeitsweise**: AI übernimmt komplette Git-Workflow-Verantwortung
**Kommunikation**: Ausschließlich Deutsch
