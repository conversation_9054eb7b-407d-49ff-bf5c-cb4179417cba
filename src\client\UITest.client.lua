-- UI Test Client - Modernisiert mit permanenten UI-Buttons
-- Testet die neuen UI-Buttons anstelle der I-Taste

print("🧪 UI Test Client wird gestartet...")

-- Services
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Lokale Variablen
local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- Einfache Test-UI erstellen
local function createTestUI()
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "TestUI"
    screenGui.Parent = playerGui
    
    local frame = Instance.new("Frame")
    frame.Name = "TestFrame"
    frame.Size = UDim2.new(0, 350, 0, 100)
    frame.Position = UDim2.new(0, 10, 0, 10)
    frame.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    frame.BackgroundTransparency = 0.3
    frame.BorderSizePixel = 0
    frame.Parent = screenGui
    
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "🎮 UI-Buttons unten | P=Shop | Q=Quests"
    label.TextColor3 = Color3.fromRGB(255, 255, 255)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSansBold
    label.Parent = frame
    
    return screenGui
end

-- Test-Input-Handler (ohne I-Taste)
local function setupTestInputHandler()
    print("🔧 Debug: Input-Handler wird eingerichtet (ohne I-Taste)...")

    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        print("🎮 Debug: Taste gedrückt:", input.KeyCode.Name, "| Processed:", gameProcessed)

        if gameProcessed then
            print("⚠️ Debug: Input wurde bereits verarbeitet, überspringe...")
            return
        end
        
        -- I-Taste entfernt wegen Roblox-Konflikt - Inventar jetzt über UI-Button verfügbar
        if input.KeyCode == Enum.KeyCode.P then
            print("✅ P-Taste gedrückt - Shop sollte sich öffnen")
            
            -- Einfaches Test-Shop erstellen
            local existingShop = playerGui:FindFirstChild("SimpleShop")
            if existingShop then
                existingShop:Destroy()
                print("🛒 Shop geschlossen")
            else
                local shopGui = Instance.new("ScreenGui")
                shopGui.Name = "SimpleShop"
                shopGui.Parent = playerGui
                
                local shopFrame = Instance.new("Frame")
                shopFrame.Size = UDim2.new(0, 500, 0, 400)
                shopFrame.Position = UDim2.new(0.5, -250, 0.5, -200)
                shopFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
                shopFrame.BorderSizePixel = 2
                shopFrame.BorderColor3 = Color3.fromRGB(100, 100, 100)
                shopFrame.Parent = shopGui
                
                local title = Instance.new("TextLabel")
                title.Size = UDim2.new(1, 0, 0, 40)
                title.BackgroundColor3 = Color3.fromRGB(20, 20, 20)
                title.BorderSizePixel = 0
                title.Text = "🛒 Test-Shop"
                title.TextColor3 = Color3.fromRGB(255, 255, 255)
                title.TextScaled = true
                title.Font = Enum.Font.SourceSansBold
                title.Parent = shopFrame
                
                local closeButton = Instance.new("TextButton")
                closeButton.Size = UDim2.new(0, 30, 0, 30)
                closeButton.Position = UDim2.new(1, -35, 0, 5)
                closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
                closeButton.BorderSizePixel = 0
                closeButton.Text = "X"
                closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
                closeButton.TextScaled = true
                closeButton.Font = Enum.Font.SourceSansBold
                closeButton.Parent = shopFrame
                
                closeButton.MouseButton1Click:Connect(function()
                    shopGui:Destroy()
                    print("🛒 Shop geschlossen")
                end)
                
                -- Test-Items hinzufügen
                local itemsLabel = Instance.new("TextLabel")
                itemsLabel.Size = UDim2.new(1, -20, 1, -60)
                itemsLabel.Position = UDim2.new(0, 10, 0, 50)
                itemsLabel.BackgroundTransparency = 1
                itemsLabel.Text = "🗡️ Schwert - 100 Coins\n🛡️ Rüstung - 150 Coins\n🧪 Trank - 25 Coins\n\n(Test-Shop - Käufe noch nicht funktional)"
                itemsLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
                itemsLabel.TextScaled = true
                itemsLabel.Font = Enum.Font.SourceSans
                itemsLabel.TextXAlignment = Enum.TextXAlignment.Left
                itemsLabel.TextYAlignment = Enum.TextYAlignment.Top
                itemsLabel.Parent = shopFrame
                
                print("🛒 Test-Shop geöffnet")
            end

        elseif input.KeyCode == Enum.KeyCode.Q then
            print("✅ Q-Taste gedrückt - Quests sollten sich öffnen")

            -- Einfaches Test-Quest-Fenster erstellen
            local existingQuests = playerGui:FindFirstChild("SimpleQuests")
            if existingQuests then
                existingQuests:Destroy()
                print("📋 Quests geschlossen")
            else
                local questGui = Instance.new("ScreenGui")
                questGui.Name = "SimpleQuests"
                questGui.Parent = playerGui

                local questFrame = Instance.new("Frame")
                questFrame.Size = UDim2.new(0, 400, 0, 350)
                questFrame.Position = UDim2.new(0.5, -200, 0.5, -175)
                questFrame.BackgroundColor3 = Color3.fromRGB(30, 60, 30)
                questFrame.BorderSizePixel = 2
                questFrame.BorderColor3 = Color3.fromRGB(100, 100, 100)
                questFrame.Parent = questGui

                local questTitle = Instance.new("TextLabel")
                questTitle.Size = UDim2.new(1, 0, 0, 40)
                questTitle.BackgroundColor3 = Color3.fromRGB(20, 40, 20)
                questTitle.BorderSizePixel = 0
                questTitle.Text = "📋 Test-Quests"
                questTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
                questTitle.TextScaled = true
                questTitle.Font = Enum.Font.SourceSansBold
                questTitle.Parent = questFrame

                local closeButton = Instance.new("TextButton")
                closeButton.Size = UDim2.new(0, 30, 0, 30)
                closeButton.Position = UDim2.new(1, -35, 0, 5)
                closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
                closeButton.BorderSizePixel = 0
                closeButton.Text = "X"
                closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
                closeButton.TextScaled = true
                closeButton.Font = Enum.Font.SourceSansBold
                closeButton.Parent = questFrame

                closeButton.MouseButton1Click:Connect(function()
                    questGui:Destroy()
                    print("📋 Quests geschlossen")
                end)

                -- Quest-Inhalt hinzufügen
                local questsLabel = Instance.new("TextLabel")
                questsLabel.Size = UDim2.new(1, -20, 1, -60)
                questsLabel.Position = UDim2.new(0, 10, 0, 50)
                questsLabel.BackgroundTransparency = 1
                questsLabel.Text = "🎯 Aktive Quests:\n\n• Sammle 10 Holz (5/10)\n• Überlebe 5 Minuten (3/5)\n• Besuche den Shop (0/1)\n\n📋 Quest-System vollständig integriert!"
                questsLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
                questsLabel.TextScaled = true
                questsLabel.Font = Enum.Font.SourceSans
                questsLabel.TextXAlignment = Enum.TextXAlignment.Left
                questsLabel.TextYAlignment = Enum.TextYAlignment.Top
                questsLabel.Parent = questFrame

                print("📋 Test-Quests geöffnet")
            end
        end
    end)
    
    print("⌨️ Input-Handler eingerichtet (P=Shop, Q=Quests)")
    print("🎒 Inventar ist jetzt über die UI-Buttons unten verfügbar!")
end

-- Initialisierung
local function initialize()
    print("🎮 UI Test wird initialisiert...")
    
    -- Test-UI erstellen
    createTestUI()
    
    -- Input-Handler einrichten
    setupTestInputHandler()
    
    print("✅ UI Test bereit! Nutze die UI-Buttons unten oder P/Q-Tasten")
end

-- Nach kurzer Verzögerung initialisieren
wait(1)
initialize()
