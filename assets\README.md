# 📁 Assets Ordner - .rbxm Dateien

Dieser Ordner enthält .rbxm Dateien, die von Rojo automatisch in den Workspace importiert werden.

## 📋 **Erwartete Dateien:**

### **TestObjects.rbxm**
- Test-Objekte für Persistenz-Test
- Enthält: TestRechteck, TestBaum, TestPlattform
- **Status:** ⏳ Noch zu erstellen

### **GameMap.rbxm** 
- Vollständige Spielwelt
- Enthält: Alle Map-Bereiche und Gebäude
- **Status:** ⏳ Noch zu erstellen

## 🎯 **So erstellen Sie .rbxm Dateien:**

1. **Objekte in Roblox Studio erstellen**
2. **In Ordner organisieren**
3. **<PERSON><PERSON>sklick → "Save to File..."**
4. **Als .rbxm in diesem Ordner speichern**

## 🔄 **Rojo Import:**

Die `default.project.json` ist bereits konfiguriert:
```json
"TestObjects": {
    "$path": "assets/TestObjects.rbxm"
},
"GameMap": {
    "$path": "assets/GameMap.rbxm"
}
```

## ✅ **Vorteile:**

- **100% Persistenz** - Objekte verschwinden nie
- **Visuelle Erstellung** - Alles in Studio
- **Versionskontrolle** - .rbxm Dateien in Git
- **Modularer Aufbau** - Separate Dateien für verschiedene Bereiche

## 🎮 **Nach dem Erstellen:**

1. **Rojo-Sync** ausführen
2. **Objekte erscheinen im Workspace**
3. **Persistenz-Test** (Spiel stoppen/starten)
4. **Objekte bleiben erhalten** ✅

Folgen Sie der Anleitung in `ROJO_RBXM_SYSTEM.md` für detaillierte Schritte!
