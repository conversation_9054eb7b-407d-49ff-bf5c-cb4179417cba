-- Test Objects - Einfache Objekte zum Testen der Persistenz
-- Dieser Code erstellt einfache Objekte um zu testen ob sie in Studio sichtbar bleiben

local TestObjects = {}

-- Einfaches Rechteck erstellen
function TestObjects.CreateTestRectangle()
    local rectangle = Instance.new("Part")
    rectangle.Name = "TestRechteck"
    rectangle.Size = Vector3.new(10, 5, 2)
    rectangle.Position = Vector3.new(0, 10, 0)
    rectangle.Color = Color3.fromRGB(255, 100, 100) -- Rot
    rectangle.Material = Enum.Material.Neon
    rectangle.Anchored = true
    rectangle.Parent = workspace
    
    print("✅ Test-Rechteck erstellt bei Position (0, 10, 0)")
    return rectangle
end

-- Einfachen Baum erstellen
function TestObjects.CreateTestTree()
    -- Baumstamm
    local trunk = Instance.new("Part")
    trunk.Name = "TestBaumstamm"
    trunk.Size = Vector3.new(2, 8, 2)
    trunk.Position = Vector3.new(10, 4, 0)
    trunk.Color = Color3.fromRGB(101, 67, 33) -- Braun
    trunk.Material = Enum.Material.Wood
    trunk.Anchored = true
    trunk.Parent = workspace
    
    -- Baumkrone
    local crown = Instance.new("Part")
    crown.Name = "TestBaumkrone"
    crown.Size = Vector3.new(6, 6, 6)
    crown.Position = Vector3.new(10, 10, 0)
    crown.Color = Color3.fromRGB(0, 128, 0) -- Grün
    crown.Material = Enum.Material.Grass
    crown.Shape = Enum.PartType.Ball
    crown.Anchored = true
    crown.Parent = workspace
    
    print("✅ Test-Baum erstellt bei Position (10, 4, 0)")
    return {trunk = trunk, crown = crown}
end

-- Test-Plattform erstellen
function TestObjects.CreateTestPlatform()
    local platform = Instance.new("Part")
    platform.Name = "TestPlattform"
    platform.Size = Vector3.new(20, 1, 20)
    platform.Position = Vector3.new(0, 0, 0)
    platform.Color = Color3.fromRGB(100, 100, 255) -- Blau
    platform.Material = Enum.Material.Marble
    platform.Anchored = true
    platform.Parent = workspace
    
    print("✅ Test-Plattform erstellt bei Position (0, 0, 0)")
    return platform
end

-- Alle Test-Objekte erstellen
function TestObjects.CreateAllTestObjects()
    print("🧪 Erstelle Test-Objekte...")
    
    TestObjects.CreateTestPlatform()
    TestObjects.CreateTestRectangle()
    TestObjects.CreateTestTree()
    
    print("🎉 Alle Test-Objekte erstellt!")
    print("📍 Schauen Sie in den Workspace:")
    print("   - TestPlattform (blau, bei 0,0,0)")
    print("   - TestRechteck (rot, bei 0,10,0)")
    print("   - TestBaumstamm + TestBaumkrone (bei 10,4,0)")
end

-- Test-Objekte entfernen
function TestObjects.RemoveAllTestObjects()
    local objectsToRemove = {"TestPlattform", "TestRechteck", "TestBaumstamm", "TestBaumkrone"}
    
    for _, objectName in ipairs(objectsToRemove) do
        local obj = workspace:FindFirstChild(objectName)
        if obj then
            obj:Destroy()
            print("🗑️ " .. objectName .. " entfernt")
        end
    end
    
    print("✅ Alle Test-Objekte entfernt")
end

return TestObjects
