-- Permanent Map Builder - <PERSON>rst<PERSON>t dauerhafte Map-Elemente in Studio
-- Diese Map bleibt auch nach dem Stoppen des Spiels erhalten

print("🗺️ Permanent Map Builder wird gestartet...")

-- Services
local Workspace = game:GetService("Workspace")
local Lighting = game:GetService("Lighting")
local RunService = game:GetService("RunService")

-- <PERSON>r<PERSON><PERSON> ob wir in Studio sind
local isStudio = RunService:IsStudio()

if not isStudio then
    print("⚠️ Permanent Map Builder läuft nur in Roblox Studio")
    return
end

print("✅ Studio erkannt - erstelle permanente Map...")

-- Konfiguration
local CONFIG = {
    WORLD_SIZE = 400,
    GROUND_HEIGHT = 3,
    
    SPAWN = {
        PLATFORM_SIZE = Vector3.new(25, 1, 25),
        PLATFORM_POS = Vector3.new(0, 2, 0),
        SPAWN_HEIGHT = 4
    },
    
    COLORS = {
        GROUND = Color3.fromRGB(76, 153, 0),
        SPAWN = Color3.fromRGB(65, 131, 215),
        SHOP = Color3.fromRGB(186, 85, 211),
        QUEST = Color3.fromRGB(50, 205, 50),
        FOREST = Color3.fromRGB(34, 139, 34),
        MINING = Color3.fromRGB(160, 82, 45),
        WATER = Color3.fromRGB(30, 144, 255),
        VILLAGE = Color3.fromRGB(255, 165, 0)
    }
}

-- Map-Container erstellen
local function createMapContainer()
    local mapFolder = Workspace:FindFirstChild("GameMap")
    if mapFolder then
        mapFolder:Destroy()
    end
    
    mapFolder = Instance.new("Folder")
    mapFolder.Name = "GameMap"
    mapFolder.Parent = Workspace
    
    return mapFolder
end

-- Hilfsfunktion für permanente Teile
local function createPermanentPart(name, size, position, color, material, transparency, parent)
    local part = Instance.new("Part")
    part.Name = name
    part.Size = size
    part.Position = position
    part.Color = color
    part.Material = material or Enum.Material.Plastic
    part.Transparency = transparency or 0
    part.Anchored = true
    part.Parent = parent or Workspace
    return part
end

-- Label erstellen
local function createPermanentLabel(text, position, color, parent)
    local label = createPermanentPart("Label_" .. text, Vector3.new(12, 1, 12), position, color, Enum.Material.Neon, 0.3, parent)
    
    local gui = Instance.new("SurfaceGui")
    gui.Face = Enum.NormalId.Top
    gui.Parent = label
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = text
    textLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.TextStrokeTransparency = 0
    textLabel.Parent = gui
    
    return label
end

-- Terrain erstellen
local function buildTerrain(mapFolder)
    print("🗺️ Baue permanentes Terrain...")
    
    local terrainFolder = Instance.new("Folder")
    terrainFolder.Name = "Terrain"
    terrainFolder.Parent = mapFolder
    
    -- Hauptboden
    createPermanentPart("MainGround", 
                       Vector3.new(CONFIG.WORLD_SIZE, CONFIG.GROUND_HEIGHT, CONFIG.WORLD_SIZE),
                       Vector3.new(0, 0, 0),
                       CONFIG.COLORS.GROUND,
                       Enum.Material.Grass,
                       0,
                       terrainFolder)
    
    print("✅ Permanentes Terrain erstellt")
end

-- Spawn-Bereich bauen
local function buildSpawn(mapFolder)
    print("🗺️ Baue permanenten Spawn...")
    
    local spawnFolder = Instance.new("Folder")
    spawnFolder.Name = "SpawnArea"
    spawnFolder.Parent = mapFolder
    
    -- Spawn-Plattform
    createPermanentPart("SpawnPlatform",
                       CONFIG.SPAWN.PLATFORM_SIZE,
                       CONFIG.SPAWN.PLATFORM_POS,
                       CONFIG.COLORS.SPAWN,
                       Enum.Material.Marble,
                       0,
                       spawnFolder)
    
    -- Spawn-Point
    local spawn = Instance.new("SpawnLocation")
    spawn.Name = "MainSpawn"
    spawn.Size = Vector3.new(4, 0.2, 4)
    spawn.Position = Vector3.new(0, CONFIG.SPAWN.SPAWN_HEIGHT, 0)
    spawn.Transparency = 0.5
    spawn.BrickColor = BrickColor.new("Bright blue")
    spawn.Material = Enum.Material.ForceField
    spawn.Anchored = true
    spawn.Parent = spawnFolder
    
    -- Label
    createPermanentLabel("🏠 SPAWN", Vector3.new(0, 8, 0), CONFIG.COLORS.SPAWN, spawnFolder)
    
    print("✅ Permanenter Spawn erstellt")
end

-- Shop bauen
local function buildShop(mapFolder)
    print("🗺️ Baue permanenten Shop...")
    
    local shopFolder = Instance.new("Folder")
    shopFolder.Name = "ShopArea"
    shopFolder.Parent = mapFolder
    
    local pos = Vector3.new(-80, 3, -80)
    
    -- Plattform
    createPermanentPart("ShopPlatform", Vector3.new(30, 2, 30), pos, CONFIG.COLORS.SHOP, Enum.Material.Marble, 0, shopFolder)
    
    -- Hauptgebäude
    createPermanentPart("ShopBuilding", Vector3.new(20, 15, 20), pos + Vector3.new(0, 8, 0), CONFIG.COLORS.SHOP, Enum.Material.Brick, 0, shopFolder)
    
    -- Dach
    createPermanentPart("ShopRoof", Vector3.new(22, 3, 22), pos + Vector3.new(0, 16, 0), Color3.fromRGB(139, 69, 19), Enum.Material.Wood, 0, shopFolder)
    
    -- Verkaufsstände
    for i = 1, 6 do
        local angle = (i - 1) * 60
        local x = pos.X + math.cos(math.rad(angle)) * 18
        local z = pos.Z + math.sin(math.rad(angle)) * 18
        createPermanentPart("Stand_" .. i, Vector3.new(3, 4, 3), Vector3.new(x, 5, z), Color3.fromRGB(139, 69, 19), Enum.Material.Wood, 0, shopFolder)
    end
    
    createPermanentLabel("🛒 MARKTPLATZ", pos + Vector3.new(0, 20, 0), CONFIG.COLORS.SHOP, shopFolder)
    print("✅ Permanenter Shop erstellt")
end

-- Quest-Bereich bauen
local function buildQuest(mapFolder)
    print("🗺️ Baue permanente Quest-Halle...")
    
    local questFolder = Instance.new("Folder")
    questFolder.Name = "QuestArea"
    questFolder.Parent = mapFolder
    
    local pos = Vector3.new(80, 3, -80)
    
    createPermanentPart("QuestPlatform", Vector3.new(30, 2, 30), pos, CONFIG.COLORS.QUEST, Enum.Material.Marble, 0, questFolder)
    createPermanentPart("QuestBuilding", Vector3.new(20, 15, 20), pos + Vector3.new(0, 8, 0), CONFIG.COLORS.QUEST, Enum.Material.Brick, 0, questFolder)
    createPermanentPart("QuestRoof", Vector3.new(22, 3, 22), pos + Vector3.new(0, 16, 0), Color3.fromRGB(139, 69, 19), Enum.Material.Wood, 0, questFolder)
    
    -- Quest-Tafeln
    for i = 1, 4 do
        local x = pos.X + (i - 2.5) * 6
        createPermanentPart("QuestBoard_" .. i, Vector3.new(4, 6, 1), Vector3.new(x, 6, pos.Z + 12), Color3.fromRGB(139, 69, 19), Enum.Material.Wood, 0, questFolder)
    end
    
    createPermanentLabel("📋 QUEST-HALLE", pos + Vector3.new(0, 20, 0), CONFIG.COLORS.QUEST, questFolder)
    print("✅ Permanente Quest-Halle erstellt")
end

-- Wald bauen
local function buildForest(mapFolder)
    print("🗺️ Baue permanenten Wald...")
    
    local forestFolder = Instance.new("Folder")
    forestFolder.Name = "ForestArea"
    forestFolder.Parent = mapFolder
    
    local centerPos = Vector3.new(-100, 2, 80)
    
    -- Waldboden
    createPermanentPart("ForestGround", Vector3.new(60, 1, 60), centerPos, CONFIG.COLORS.FOREST, Enum.Material.Grass, 0, forestFolder)
    
    -- Bäume
    for i = 1, 20 do
        local x = centerPos.X + math.random(-25, 25)
        local z = centerPos.Z + math.random(-25, 25)
        local height = math.random(8, 15)
        
        -- Stamm
        createPermanentPart("TreeTrunk_" .. i, Vector3.new(2, height, 2), Vector3.new(x, centerPos.Y + height/2, z), 
                           Color3.fromRGB(101, 67, 33), Enum.Material.Wood, 0, forestFolder)
        
        -- Krone
        local crown = createPermanentPart("TreeCrown_" .. i, Vector3.new(8, 8, 8), Vector3.new(x, centerPos.Y + height + 2, z), 
                                         Color3.fromRGB(0, 128, 0), Enum.Material.Grass, 0, forestFolder)
        crown.Shape = Enum.PartType.Ball
    end
    
    createPermanentLabel("🌲 MYSTISCHER WALD", centerPos + Vector3.new(0, 15, 0), CONFIG.COLORS.FOREST, forestFolder)
    print("✅ Permanenter Wald erstellt")
end

-- Mining bauen
local function buildMining(mapFolder)
    print("🗺️ Baue permanente Mine...")
    
    local miningFolder = Instance.new("Folder")
    miningFolder.Name = "MiningArea"
    miningFolder.Parent = mapFolder
    
    local centerPos = Vector3.new(100, 2, 80)
    
    -- Mining-Boden
    createPermanentPart("MiningGround", Vector3.new(50, 1, 50), centerPos, CONFIG.COLORS.MINING, Enum.Material.Rock, 0, miningFolder)
    
    -- Felsen und Erze
    for i = 1, 15 do
        local x = centerPos.X + math.random(-20, 20)
        local z = centerPos.Z + math.random(-20, 20)
        local size = math.random(3, 8)
        
        -- Felsen
        createPermanentPart("Rock_" .. i, Vector3.new(size, size, size), Vector3.new(x, centerPos.Y + size/2, z), 
                           Color3.fromRGB(105, 105, 105), Enum.Material.Rock, 0, miningFolder)
        
        -- Erze
        if math.random() > 0.6 then
            local oreColors = {
                Color3.fromRGB(255, 215, 0),    -- Gold
                Color3.fromRGB(169, 169, 169),  -- Silber
                Color3.fromRGB(185, 242, 255)   -- Diamant
            }
            local oreColor = oreColors[math.random(1, #oreColors)]
            createPermanentPart("Ore_" .. i, Vector3.new(2, 2, 2), Vector3.new(x + 2, centerPos.Y + 2, z + 2), 
                               oreColor, Enum.Material.Neon, 0, miningFolder)
        end
    end
    
    createPermanentLabel("⛏️ KRISTALL-MINE", centerPos + Vector3.new(0, 12, 0), CONFIG.COLORS.MINING, miningFolder)
    print("✅ Permanente Mine erstellt")
end

-- Wasser bauen
local function buildWater(mapFolder)
    print("🗺️ Baue permanenten See...")
    
    local waterFolder = Instance.new("Folder")
    waterFolder.Name = "WaterArea"
    waterFolder.Parent = mapFolder
    
    local pos = Vector3.new(0, -3, 120)
    
    -- Wasser
    local water = createPermanentPart("WaterArea", Vector3.new(80, 12, 80), pos, CONFIG.COLORS.WATER, Enum.Material.ForceField, 0.6, waterFolder)
    water.CanCollide = false
    
    -- Kleine Inseln
    for i = 1, 3 do
        local x = pos.X + math.random(-30, 30)
        local z = pos.Z + math.random(-30, 30)
        createPermanentPart("Island_" .. i, Vector3.new(8, 2, 8), Vector3.new(x, 1, z), CONFIG.COLORS.GROUND, Enum.Material.Grass, 0, waterFolder)
    end
    
    createPermanentLabel("🌊 KRISTALLSEE", Vector3.new(0, 8, 120), CONFIG.COLORS.WATER, waterFolder)
    print("✅ Permanenter See erstellt")
end

-- Dorf bauen
local function buildVillage(mapFolder)
    print("🗺️ Baue permanentes Dorf...")
    
    local villageFolder = Instance.new("Folder")
    villageFolder.Name = "VillageArea"
    villageFolder.Parent = mapFolder
    
    local centerPos = Vector3.new(-120, 2, -20)
    
    -- Dorfplatz
    createPermanentPart("VillageSquare", Vector3.new(40, 1, 40), centerPos, CONFIG.COLORS.VILLAGE, Enum.Material.Concrete, 0, villageFolder)
    
    -- Häuser
    local housePositions = {
        Vector3.new(-130, 5, -30), Vector3.new(-110, 5, -30),
        Vector3.new(-130, 5, -10), Vector3.new(-110, 5, -10),
        Vector3.new(-120, 5, 0)
    }
    
    for i, housePos in ipairs(housePositions) do
        createPermanentPart("House_" .. i, Vector3.new(8, 8, 8), housePos, Color3.fromRGB(178, 34, 34), Enum.Material.Brick, 0, villageFolder)
        createPermanentPart("HouseRoof_" .. i, Vector3.new(10, 2, 10), housePos + Vector3.new(0, 5, 0), Color3.fromRGB(139, 69, 19), Enum.Material.Wood, 0, villageFolder)
    end
    
    -- Brunnen
    createPermanentPart("WellBase", Vector3.new(4, 3, 4), centerPos + Vector3.new(0, 1.5, 0), Color3.fromRGB(105, 105, 105), Enum.Material.Concrete, 0, villageFolder)
    createPermanentPart("WellWater", Vector3.new(2, 1, 2), centerPos + Vector3.new(0, 2.5, 0), CONFIG.COLORS.WATER, Enum.Material.ForceField, 0.5, villageFolder)
    
    createPermanentLabel("🏘️ FRIEDLICHES DORF", centerPos + Vector3.new(0, 12, 0), CONFIG.COLORS.VILLAGE, villageFolder)
    print("✅ Permanentes Dorf erstellt")
end

-- Beleuchtung einrichten
local function setupPermanentLighting()
    print("🗺️ Richte permanente Beleuchtung ein...")
    
    Lighting.Brightness = 2.5
    Lighting.Ambient = Color3.fromRGB(120, 120, 120)
    Lighting.TimeOfDay = "14:00:00"
    Lighting.FogEnd = 2000
    
    -- Skybox
    if not Lighting:FindFirstChild("Sky") then
        local sky = Instance.new("Sky")
        sky.SkyboxBk = "rbxasset://textures/sky/sky512_bk.tex"
        sky.SkyboxDn = "rbxasset://textures/sky/sky512_dn.tex"
        sky.SkyboxFt = "rbxasset://textures/sky/sky512_ft.tex"
        sky.SkyboxLf = "rbxasset://textures/sky/sky512_lf.tex"
        sky.SkyboxRt = "rbxasset://textures/sky/sky512_rt.tex"
        sky.SkyboxUp = "rbxasset://textures/sky/sky512_up.tex"
        sky.Parent = Lighting
    end
    
    print("✅ Permanente Beleuchtung eingerichtet")
end

-- Hauptfunktion
local function buildPermanentMap()
    print("🗺️ Starte permanente Map-Erstellung...")
    
    -- Erstelle Map-Container
    local mapFolder = createMapContainer()
    
    -- Baue alle Bereiche
    buildTerrain(mapFolder)
    buildSpawn(mapFolder)
    buildShop(mapFolder)
    buildQuest(mapFolder)
    buildForest(mapFolder)
    buildMining(mapFolder)
    buildWater(mapFolder)
    buildVillage(mapFolder)
    setupPermanentLighting()
    
    print("✅ Permanente Map-Erstellung abgeschlossen!")
    print("🎮 Map bleibt jetzt auch nach dem Stoppen erhalten!")
    print("📁 Alle Elemente sind im 'GameMap' Ordner organisiert")
end

-- Map erstellen
wait(2)
buildPermanentMap()
print("🎮 Permanente Map ist bereit und bleibt erhalten!")
