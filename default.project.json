{"name": "MultifunktionalesRobloxSpiel", "tree": {"$className": "DataModel", "ReplicatedStorage": {"$className": "ReplicatedStorage", "Shared": {"$path": "src/shared"}, "Modules": {"$path": "src/modules"}, "Components": {"$path": "src/components"}, "Data": {"$path": "src/data"}, "Events": {"$className": "Folder"}, "Functions": {"$className": "Folder"}}, "ServerScriptService": {"$className": "ServerScriptService", "Server": {"$path": "src/server"}, "Services": {"$path": "src/services"}, "StudioMapBuilder": {"$path": "src/studio/MapBuilderPlugin.server.lua"}}, "StarterPlayer": {"$className": "StarterPlayer", "StarterPlayerScripts": {"$className": "StarterPlayerScripts", "Client": {"$path": "src/client"}}}, "StarterGui": {"$className": "<PERSON><PERSON><PERSON><PERSON>"}, "Workspace": {"$className": "Workspace", "Utils": {"$path": "src/utils"}}}}