-- Studio Map Builder Plugin - Erstellt permanente Map direkt im Studio
-- Diese Map bleibt dauerhaft erhalten, auch ohne Play-Modus

print("🗺️ Studio Map Builder Plugin wird gestartet...")

-- Services
local Workspace = game:GetService("Workspace")
local Lighting = game:GetService("Lighting")

-- Konfiguration
local CONFIG = {
    WORLD_SIZE = 400,
    GROUND_HEIGHT = 3,
    
    SPAWN = {
        PLATFORM_SIZE = Vector3.new(25, 1, 25),
        PLATFORM_POS = Vector3.new(0, 2, 0),
        SPAWN_HEIGHT = 4
    },
    
    COLORS = {
        GROUND = Color3.fromRGB(76, 153, 0),
        SPAWN = Color3.fromRGB(65, 131, 215),
        SHOP = Color3.fromRGB(186, 85, 211),
        QUEST = Color3.fromRGB(50, 205, 50),
        FOREST = Color3.fromRGB(34, 139, 34),
        MINING = Color3.fromRGB(160, 82, 45),
        WATER = Color3.fromRGB(30, 144, 255),
        VILLAGE = Color3.fromRGB(255, 165, 0)
    }
}

-- Map-Container erstellen
local function createMapContainer()
    local mapFolder = Workspace:FindFirstChild("PermanentGameMap")
    if mapFolder then
        print("🗺️ Lösche alte Map...")
        mapFolder:Destroy()
        wait(0.5)
    end
    
    mapFolder = Instance.new("Folder")
    mapFolder.Name = "PermanentGameMap"
    mapFolder.Parent = Workspace
    
    print("📁 Map-Container erstellt: " .. mapFolder.Name)
    return mapFolder
end

-- Hilfsfunktion für dauerhafte Teile
local function createStudioPart(name, size, position, color, material, transparency, parent)
    local part = Instance.new("Part")
    part.Name = name
    part.Size = size
    part.Position = position
    part.Color = color
    part.Material = material or Enum.Material.Plastic
    part.Transparency = transparency or 0
    part.Anchored = true
    part.Parent = parent or Workspace
    
    print("🔧 Erstellt: " .. name)
    return part
end

-- Label erstellen
local function createStudioLabel(text, position, color, parent)
    local label = createStudioPart("Label_" .. text, Vector3.new(12, 1, 12), position, color, Enum.Material.Neon, 0.3, parent)
    
    local gui = Instance.new("SurfaceGui")
    gui.Face = Enum.NormalId.Top
    gui.Parent = label
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = text
    textLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.TextStrokeTransparency = 0
    textLabel.Parent = gui
    
    return label
end

-- Terrain erstellen
local function buildStudioTerrain(mapFolder)
    print("🗺️ Baue Studio-Terrain...")
    
    local terrainFolder = Instance.new("Folder")
    terrainFolder.Name = "Terrain"
    terrainFolder.Parent = mapFolder
    
    -- Hauptboden
    createStudioPart("MainGround", 
                    Vector3.new(CONFIG.WORLD_SIZE, CONFIG.GROUND_HEIGHT, CONFIG.WORLD_SIZE),
                    Vector3.new(0, 0, 0),
                    CONFIG.COLORS.GROUND,
                    Enum.Material.Grass,
                    0,
                    terrainFolder)
    
    print("✅ Studio-Terrain erstellt")
end

-- Spawn-Bereich bauen
local function buildStudioSpawn(mapFolder)
    print("🗺️ Baue Studio-Spawn...")
    
    local spawnFolder = Instance.new("Folder")
    spawnFolder.Name = "SpawnArea"
    spawnFolder.Parent = mapFolder
    
    -- Spawn-Plattform
    createStudioPart("SpawnPlatform",
                    CONFIG.SPAWN.PLATFORM_SIZE,
                    CONFIG.SPAWN.PLATFORM_POS,
                    CONFIG.COLORS.SPAWN,
                    Enum.Material.Marble,
                    0,
                    spawnFolder)
    
    -- Spawn-Point
    local spawn = Instance.new("SpawnLocation")
    spawn.Name = "MainSpawn"
    spawn.Size = Vector3.new(4, 0.2, 4)
    spawn.Position = Vector3.new(0, CONFIG.SPAWN.SPAWN_HEIGHT, 0)
    spawn.Transparency = 0.5
    spawn.BrickColor = BrickColor.new("Bright blue")
    spawn.Material = Enum.Material.ForceField
    spawn.Anchored = true
    spawn.Parent = spawnFolder
    
    -- Label
    createStudioLabel("🏠 SPAWN", Vector3.new(0, 8, 0), CONFIG.COLORS.SPAWN, spawnFolder)
    
    print("✅ Studio-Spawn erstellt")
end

-- Shop bauen
local function buildStudioShop(mapFolder)
    print("🗺️ Baue Studio-Shop...")
    
    local shopFolder = Instance.new("Folder")
    shopFolder.Name = "ShopArea"
    shopFolder.Parent = mapFolder
    
    local pos = Vector3.new(-80, 3, -80)
    
    -- Plattform
    createStudioPart("ShopPlatform", Vector3.new(30, 2, 30), pos, CONFIG.COLORS.SHOP, Enum.Material.Marble, 0, shopFolder)
    
    -- Hauptgebäude
    createStudioPart("ShopBuilding", Vector3.new(20, 15, 20), pos + Vector3.new(0, 8, 0), CONFIG.COLORS.SHOP, Enum.Material.Brick, 0, shopFolder)
    
    -- Dach
    createStudioPart("ShopRoof", Vector3.new(22, 3, 22), pos + Vector3.new(0, 16, 0), Color3.fromRGB(139, 69, 19), Enum.Material.Wood, 0, shopFolder)
    
    -- Verkaufsstände
    for i = 1, 6 do
        local angle = (i - 1) * 60
        local x = pos.X + math.cos(math.rad(angle)) * 18
        local z = pos.Z + math.sin(math.rad(angle)) * 18
        createStudioPart("Stand_" .. i, Vector3.new(3, 4, 3), Vector3.new(x, 5, z), Color3.fromRGB(139, 69, 19), Enum.Material.Wood, 0, shopFolder)
    end
    
    createStudioLabel("🛒 MARKTPLATZ", pos + Vector3.new(0, 20, 0), CONFIG.COLORS.SHOP, shopFolder)
    print("✅ Studio-Shop erstellt")
end

-- Quest-Bereich bauen
local function buildStudioQuest(mapFolder)
    print("🗺️ Baue Studio-Quest-Halle...")
    
    local questFolder = Instance.new("Folder")
    questFolder.Name = "QuestArea"
    questFolder.Parent = mapFolder
    
    local pos = Vector3.new(80, 3, -80)
    
    createStudioPart("QuestPlatform", Vector3.new(30, 2, 30), pos, CONFIG.COLORS.QUEST, Enum.Material.Marble, 0, questFolder)
    createStudioPart("QuestBuilding", Vector3.new(20, 15, 20), pos + Vector3.new(0, 8, 0), CONFIG.COLORS.QUEST, Enum.Material.Brick, 0, questFolder)
    createStudioPart("QuestRoof", Vector3.new(22, 3, 22), pos + Vector3.new(0, 16, 0), Color3.fromRGB(139, 69, 19), Enum.Material.Wood, 0, questFolder)
    
    -- Quest-Tafeln
    for i = 1, 4 do
        local x = pos.X + (i - 2.5) * 6
        createStudioPart("QuestBoard_" .. i, Vector3.new(4, 6, 1), Vector3.new(x, 6, pos.Z + 12), Color3.fromRGB(139, 69, 19), Enum.Material.Wood, 0, questFolder)
    end
    
    createStudioLabel("📋 QUEST-HALLE", pos + Vector3.new(0, 20, 0), CONFIG.COLORS.QUEST, questFolder)
    print("✅ Studio-Quest-Halle erstellt")
end

-- Wald bauen (vereinfacht für bessere Performance)
local function buildStudioForest(mapFolder)
    print("🗺️ Baue Studio-Wald...")
    
    local forestFolder = Instance.new("Folder")
    forestFolder.Name = "ForestArea"
    forestFolder.Parent = mapFolder
    
    local centerPos = Vector3.new(-100, 2, 80)
    
    -- Waldboden
    createStudioPart("ForestGround", Vector3.new(60, 1, 60), centerPos, CONFIG.COLORS.FOREST, Enum.Material.Grass, 0, forestFolder)
    
    -- Weniger Bäume für bessere Performance
    for i = 1, 10 do
        local x = centerPos.X + (i - 5.5) * 8
        local z = centerPos.Z + math.random(-20, 20)
        local height = 12
        
        -- Stamm
        createStudioPart("TreeTrunk_" .. i, Vector3.new(2, height, 2), Vector3.new(x, centerPos.Y + height/2, z), 
                        Color3.fromRGB(101, 67, 33), Enum.Material.Wood, 0, forestFolder)
        
        -- Krone
        local crown = createStudioPart("TreeCrown_" .. i, Vector3.new(8, 8, 8), Vector3.new(x, centerPos.Y + height + 2, z), 
                                      Color3.fromRGB(0, 128, 0), Enum.Material.Grass, 0, forestFolder)
        crown.Shape = Enum.PartType.Ball
    end
    
    createStudioLabel("🌲 MYSTISCHER WALD", centerPos + Vector3.new(0, 15, 0), CONFIG.COLORS.FOREST, forestFolder)
    print("✅ Studio-Wald erstellt")
end

-- Mining bauen (vereinfacht)
local function buildStudioMining(mapFolder)
    print("🗺️ Baue Studio-Mine...")
    
    local miningFolder = Instance.new("Folder")
    miningFolder.Name = "MiningArea"
    miningFolder.Parent = mapFolder
    
    local centerPos = Vector3.new(100, 2, 80)
    
    -- Mining-Boden
    createStudioPart("MiningGround", Vector3.new(50, 1, 50), centerPos, CONFIG.COLORS.MINING, Enum.Material.Rock, 0, miningFolder)
    
    -- Weniger Felsen für bessere Performance
    for i = 1, 8 do
        local x = centerPos.X + (i - 4.5) * 8
        local z = centerPos.Z + math.random(-15, 15)
        local size = 5
        
        -- Felsen
        createStudioPart("Rock_" .. i, Vector3.new(size, size, size), Vector3.new(x, centerPos.Y + size/2, z), 
                        Color3.fromRGB(105, 105, 105), Enum.Material.Rock, 0, miningFolder)
        
        -- Erz
        local oreColor = Color3.fromRGB(255, 215, 0) -- Gold
        createStudioPart("Ore_" .. i, Vector3.new(2, 2, 2), Vector3.new(x + 2, centerPos.Y + 2, z + 2), 
                        oreColor, Enum.Material.Neon, 0, miningFolder)
    end
    
    createStudioLabel("⛏️ KRISTALL-MINE", centerPos + Vector3.new(0, 12, 0), CONFIG.COLORS.MINING, miningFolder)
    print("✅ Studio-Mine erstellt")
end

-- Wasser bauen
local function buildStudioWater(mapFolder)
    print("🗺️ Baue Studio-See...")
    
    local waterFolder = Instance.new("Folder")
    waterFolder.Name = "WaterArea"
    waterFolder.Parent = mapFolder
    
    local pos = Vector3.new(0, -3, 120)
    
    -- Wasser
    local water = createStudioPart("WaterArea", Vector3.new(80, 12, 80), pos, CONFIG.COLORS.WATER, Enum.Material.ForceField, 0.6, waterFolder)
    water.CanCollide = false
    
    -- Eine kleine Insel
    createStudioPart("CenterIsland", Vector3.new(12, 2, 12), Vector3.new(0, 1, 120), CONFIG.COLORS.GROUND, Enum.Material.Grass, 0, waterFolder)
    
    createStudioLabel("🌊 KRISTALLSEE", Vector3.new(0, 8, 120), CONFIG.COLORS.WATER, waterFolder)
    print("✅ Studio-See erstellt")
end

-- Beleuchtung einrichten
local function setupStudioLighting()
    print("🗺️ Richte Studio-Beleuchtung ein...")
    
    Lighting.Brightness = 2.5
    Lighting.Ambient = Color3.fromRGB(120, 120, 120)
    Lighting.TimeOfDay = "14:00:00"
    Lighting.FogEnd = 2000
    
    print("✅ Studio-Beleuchtung eingerichtet")
end

-- Hauptfunktion
local function buildStudioMap()
    print("🗺️ Starte Studio-Map-Erstellung...")
    print("⏳ Dies kann einen Moment dauern...")
    
    -- Erstelle Map-Container
    local mapFolder = createMapContainer()
    
    -- Baue alle Bereiche (mit Pausen für bessere Performance)
    buildStudioTerrain(mapFolder)
    wait(0.5)
    
    buildStudioSpawn(mapFolder)
    wait(0.5)
    
    buildStudioShop(mapFolder)
    wait(0.5)
    
    buildStudioQuest(mapFolder)
    wait(0.5)
    
    buildStudioForest(mapFolder)
    wait(0.5)
    
    buildStudioMining(mapFolder)
    wait(0.5)
    
    buildStudioWater(mapFolder)
    wait(0.5)
    
    setupStudioLighting()
    
    print("✅ Studio-Map-Erstellung abgeschlossen!")
    print("🎮 Map ist jetzt DAUERHAFT im Workspace gespeichert!")
    print("📁 Schauen Sie in den 'PermanentGameMap' Ordner im Workspace")
    print("💾 Diese Map bleibt auch nach dem Schließen von Studio erhalten!")
end

-- Map sofort erstellen
buildStudioMap()
print("🎮 Permanente Studio-Map ist bereit!")
