-- Components Module
-- Enthält alle UI-Komponenten

local Components = {}

-- Services
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Nur auf Client ausführen
if RunService:IsServer() then
    return Components
end

-- Module laden (warten bis verfügbar)
local EventBus = require(ReplicatedStorage:WaitForChild("Modules"):WaitForChild("EventBus"))
local Logger = require(ReplicatedStorage:WaitForChild("Shared"):WaitForChild("Logger"))

-- Lokale Variablen
local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- Inventory UI erstellen
function Components.CreateInventoryUI()
    -- Hauptframe
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "InventoryUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui

    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "InventoryFrame"
    mainFrame.Size = UDim2.new(0, 600, 0, 400)
    mainFrame.Position = UDim2.new(0.5, -300, 0.5, -200)
    mainFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui

    -- Abgerundete Ecken für Inventar
    local inventoryCorner = Instance.new("UICorner")
    inventoryCorner.CornerRadius = UDim.new(0, 12)
    inventoryCorner.Parent = mainFrame

    -- Titel
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "Title"
    titleLabel.Size = UDim2.new(1, 0, 0, 40)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
    titleLabel.BorderSizePixel = 0
    titleLabel.Text = "🎒 Inventar"
    titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = mainFrame

    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -35, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = mainFrame

    -- Schließen-Button Event-Handler
    closeButton.MouseButton1Click:Connect(function()
        Components.ToggleInventory()
    end)

    -- Items-Container
    local itemsFrame = Instance.new("ScrollingFrame")
    itemsFrame.Name = "ItemsFrame"
    itemsFrame.Size = UDim2.new(0.7, -10, 1, -50)
    itemsFrame.Position = UDim2.new(0, 5, 0, 45)
    itemsFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    itemsFrame.BorderSizePixel = 0
    itemsFrame.ScrollBarThickness = 8
    itemsFrame.Parent = mainFrame

    -- Equipment-Frame
    local equipmentFrame = Instance.new("Frame")
    equipmentFrame.Name = "EquipmentFrame"
    equipmentFrame.Size = UDim2.new(0.3, -10, 1, -50)
    equipmentFrame.Position = UDim2.new(0.7, 5, 0, 45)
    equipmentFrame.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    equipmentFrame.BorderSizePixel = 0
    equipmentFrame.Parent = mainFrame

    -- Equipment-Titel
    local equipTitle = Instance.new("TextLabel")
    equipTitle.Name = "EquipTitle"
    equipTitle.Size = UDim2.new(1, 0, 0, 30)
    equipTitle.Position = UDim2.new(0, 0, 0, 0)
    equipTitle.BackgroundTransparency = 1
    equipTitle.Text = "⚔️ Ausrüstung"
    equipTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
    equipTitle.TextScaled = true
    equipTitle.Font = Enum.Font.SourceSansBold
    equipTitle.Parent = equipmentFrame

    return screenGui
end

-- Inventory UI Toggle
function Components.ToggleInventory()
    print("🎒 Debug: ToggleInventory aufgerufen")

    local inventoryUI = playerGui:FindFirstChild("InventoryUI")
    print("🎒 Debug: Existierendes InventoryUI gefunden:", inventoryUI ~= nil)

    if not inventoryUI then
        print("🎒 Debug: Erstelle neues InventoryUI...")
        inventoryUI = Components.CreateInventoryUI()
        print("🎒 Debug: InventoryUI erstellt:", inventoryUI ~= nil)
    end

    local mainFrame = inventoryUI.InventoryFrame
    print("🎒 Debug: MainFrame gefunden:", mainFrame ~= nil)
    print("🎒 Debug: MainFrame aktuell sichtbar:", mainFrame.Visible)

    mainFrame.Visible = not mainFrame.Visible
    print("🎒 Debug: MainFrame neue Sichtbarkeit:", mainFrame.Visible)

    if mainFrame.Visible then
        -- Inventar-Daten anfordern
        EventBus:FireServer("GetInventory")
        Logger.info("Inventar geöffnet")
        print("🎒 Debug: Inventar-Daten angefordert")

        -- Fallback: Zeige Test-Inventar wenn keine Daten kommen
        wait(1)
        Components.ShowTestInventory()
    else
        Logger.info("Inventar geschlossen")
        print("🎒 Debug: Inventar geschlossen")
    end
end

-- Input-Handler (ohne I-Taste wegen Roblox-Konflikt)
function Components.SetupInputHandlers()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end

        -- Nur P und Q-Tasten als Shortcuts behalten
        if input.KeyCode == Enum.KeyCode.P then
            Components.ToggleShop()
        elseif input.KeyCode == Enum.KeyCode.Q then
            Components.ToggleQuests()
        end
    end)

    Logger.info("Input-Handler eingerichtet (P = Shop, Q = Quests) - Inventar über UI-Button")
end

-- Event-Handler für Inventar-Updates
function Components.SetupInventoryEvents()
    EventBus:OnClientEvent("InventoryData", function(inventoryData)
        Components.UpdateInventoryDisplay(inventoryData)
    end)

    EventBus:OnClientEvent("InventoryUpdated", function(inventoryData)
        Components.UpdateInventoryDisplay(inventoryData)
    end)

    Logger.info("Inventar Event-Handler eingerichtet")
end

-- Test-Inventar anzeigen (Fallback)
function Components.ShowTestInventory()
    print("🎒 Debug: ShowTestInventory aufgerufen")

    local inventoryUI = playerGui:FindFirstChild("InventoryUI")
    if not inventoryUI then
        print("🎒 Debug: Kein InventoryUI gefunden für Test")
        return
    end

    local itemsFrame = inventoryUI.InventoryFrame.ItemsFrame
    print("🎒 Debug: ItemsFrame gefunden:", itemsFrame ~= nil)

    -- Lösche alte Items
    for _, child in ipairs(itemsFrame:GetChildren()) do
        if child:IsA("Frame") then
            child:Destroy()
        end
    end

    -- Erstelle Test-Items
    local testItems = {
        {Name = "Basis Schwert", Color = Color3.fromRGB(255, 255, 255), Quantity = 1},
        {Name = "Heiltrank", Color = Color3.fromRGB(0, 255, 0), Quantity = 5},
        {Name = "Holz", Color = Color3.fromRGB(139, 69, 19), Quantity = 10},
        {Name = "Eisenschwert", Color = Color3.fromRGB(0, 100, 255), Quantity = 1}
    }

    local slotSize = 80
    local padding = 10
    local slotsPerRow = 5

    for i, item in ipairs(testItems) do
        local row = math.floor((i - 1) / slotsPerRow)
        local col = (i - 1) % slotsPerRow

        local itemSlot = Instance.new("Frame")
        itemSlot.Name = "TestItemSlot_" .. i
        itemSlot.Size = UDim2.new(0, slotSize, 0, slotSize)
        itemSlot.Position = UDim2.new(0, col * (slotSize + padding) + padding, 0, row * (slotSize + padding) + padding)
        itemSlot.BackgroundColor3 = item.Color
        itemSlot.BorderSizePixel = 2
        itemSlot.BorderColor3 = Color3.fromRGB(100, 100, 100)
        itemSlot.Parent = itemsFrame

        -- Abgerundete Ecken für Item-Slots
        local slotCorner = Instance.new("UICorner")
        slotCorner.CornerRadius = UDim.new(0, 8)
        slotCorner.Parent = itemSlot

        -- Item-Name
        local itemLabel = Instance.new("TextLabel")
        itemLabel.Size = UDim2.new(1, 0, 0.7, 0)
        itemLabel.Position = UDim2.new(0, 0, 0, 0)
        itemLabel.BackgroundTransparency = 1
        itemLabel.Text = item.Name
        itemLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        itemLabel.TextScaled = true
        itemLabel.Font = Enum.Font.SourceSansBold
        itemLabel.Parent = itemSlot

        -- Anzahl
        if item.Quantity > 1 then
            local quantityLabel = Instance.new("TextLabel")
            quantityLabel.Size = UDim2.new(1, 0, 0.3, 0)
            quantityLabel.Position = UDim2.new(0, 0, 0.7, 0)
            quantityLabel.BackgroundTransparency = 1
            quantityLabel.Text = "x" .. item.Quantity
            quantityLabel.TextColor3 = Color3.fromRGB(255, 255, 0)
            quantityLabel.TextScaled = true
            quantityLabel.Font = Enum.Font.SourceSansBold
            quantityLabel.Parent = itemSlot
        end
    end

    -- Canvas-Größe anpassen
    local totalRows = math.ceil(#testItems / slotsPerRow)
    itemsFrame.CanvasSize = UDim2.new(0, 0, 0, totalRows * (slotSize + padding) + padding)

    print("🎒 Debug: Test-Inventar mit", #testItems, "Items erstellt")
end

-- Inventar-Anzeige aktualisieren
function Components.UpdateInventoryDisplay(inventoryData)
    local inventoryUI = playerGui:FindFirstChild("InventoryUI")
    if not inventoryUI then return end

    local itemsFrame = inventoryUI.InventoryFrame.ItemsFrame

    -- Lösche alte Items
    for _, child in ipairs(itemsFrame:GetChildren()) do
        if child:IsA("Frame") then
            child:Destroy()
        end
    end

    -- Erstelle Item-Slots
    local slotSize = 60
    local padding = 5
    local slotsPerRow = math.floor((itemsFrame.AbsoluteSize.X - padding) / (slotSize + padding))

    for i, item in ipairs(inventoryData.Items) do
        local row = math.floor((i - 1) / slotsPerRow)
        local col = (i - 1) % slotsPerRow

        local itemSlot = Instance.new("Frame")
        itemSlot.Name = "ItemSlot_" .. i
        itemSlot.Size = UDim2.new(0, slotSize, 0, slotSize)
        itemSlot.Position = UDim2.new(0, col * (slotSize + padding) + padding, 0, row * (slotSize + padding) + padding)
        itemSlot.BackgroundColor3 = item.Data.Rarity.Color
        itemSlot.BorderSizePixel = 1
        itemSlot.BorderColor3 = Color3.fromRGB(100, 100, 100)
        itemSlot.Parent = itemsFrame

        -- Item-Name
        local itemLabel = Instance.new("TextLabel")
        itemLabel.Size = UDim2.new(1, 0, 0.7, 0)
        itemLabel.Position = UDim2.new(0, 0, 0, 0)
        itemLabel.BackgroundTransparency = 1
        itemLabel.Text = item.Data.Name
        itemLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        itemLabel.TextScaled = true
        itemLabel.Font = Enum.Font.SourceSans
        itemLabel.Parent = itemSlot

        -- Anzahl
        if item.Quantity > 1 then
            local quantityLabel = Instance.new("TextLabel")
            quantityLabel.Size = UDim2.new(1, 0, 0.3, 0)
            quantityLabel.Position = UDim2.new(0, 0, 0.7, 0)
            quantityLabel.BackgroundTransparency = 1
            quantityLabel.Text = "x" .. item.Quantity
            quantityLabel.TextColor3 = Color3.fromRGB(255, 255, 0)
            quantityLabel.TextScaled = true
            quantityLabel.Font = Enum.Font.SourceSansBold
            quantityLabel.Parent = itemSlot
        end
    end

    -- Canvas-Größe anpassen
    local totalRows = math.ceil(#inventoryData.Items / slotsPerRow)
    itemsFrame.CanvasSize = UDim2.new(0, 0, 0, totalRows * (slotSize + padding) + padding)

    Logger.info("Inventar-Anzeige aktualisiert: %d Items", #inventoryData.Items)
end

-- Shop UI erstellen
function Components.CreateShopUI()
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "ShopUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui

    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "ShopFrame"
    mainFrame.Size = UDim2.new(0, 800, 0, 500)
    mainFrame.Position = UDim2.new(0.5, -400, 0.5, -250)
    mainFrame.BackgroundColor3 = Color3.fromRGB(35, 35, 35)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui

    -- Titel
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "Title"
    titleLabel.Size = UDim2.new(1, 0, 0, 40)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.fromRGB(25, 25, 25)
    titleLabel.BorderSizePixel = 0
    titleLabel.Text = "🛒 Shop"
    titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = mainFrame

    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -35, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = mainFrame

    closeButton.MouseButton1Click:Connect(function()
        Components.ToggleShop()
    end)

    -- Währungs-Anzeige
    local currencyFrame = Instance.new("Frame")
    currencyFrame.Name = "CurrencyFrame"
    currencyFrame.Size = UDim2.new(1, 0, 0, 30)
    currencyFrame.Position = UDim2.new(0, 0, 0, 45)
    currencyFrame.BackgroundColor3 = Color3.fromRGB(45, 45, 45)
    currencyFrame.BorderSizePixel = 0
    currencyFrame.Parent = mainFrame

    -- Kategorien-Frame
    local categoriesFrame = Instance.new("Frame")
    categoriesFrame.Name = "CategoriesFrame"
    categoriesFrame.Size = UDim2.new(0, 150, 1, -80)
    categoriesFrame.Position = UDim2.new(0, 5, 0, 80)
    categoriesFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    categoriesFrame.BorderSizePixel = 0
    categoriesFrame.Parent = mainFrame

    -- Items-Frame
    local itemsFrame = Instance.new("ScrollingFrame")
    itemsFrame.Name = "ItemsFrame"
    itemsFrame.Size = UDim2.new(1, -165, 1, -80)
    itemsFrame.Position = UDim2.new(0, 160, 0, 80)
    itemsFrame.BackgroundColor3 = Color3.fromRGB(55, 55, 55)
    itemsFrame.BorderSizePixel = 0
    itemsFrame.ScrollBarThickness = 8
    itemsFrame.Parent = mainFrame

    return screenGui
end

-- Shop UI Toggle
function Components.ToggleShop()
    local shopUI = playerGui:FindFirstChild("ShopUI")
    if not shopUI then
        shopUI = Components.CreateShopUI()
    end

    local mainFrame = shopUI.ShopFrame
    mainFrame.Visible = not mainFrame.Visible

    if mainFrame.Visible then
        EventBus:FireServer("GetShopData")
        EventBus:FireServer("GetEconomyData")
        Logger.info("Shop geöffnet")
    else
        Logger.info("Shop geschlossen")
    end
end

-- Event-Handler für Shop-Updates
function Components.SetupShopEvents()
    EventBus:OnClientEvent("ShopData", function(shopData)
        Components.UpdateShopDisplay(shopData)
    end)

    EventBus:OnClientEvent("EconomyData", function(economyData)
        Components.UpdateCurrencyDisplay(economyData.Currencies)
    end)

    EventBus:OnClientEvent("CurrencyUpdated", function(currencies)
        Components.UpdateCurrencyDisplay(currencies)
    end)

    EventBus:OnClientEvent("PurchaseResult", function(success, message)
        Components.ShowPurchaseResult(success, message)
    end)

    Logger.info("Shop Event-Handler eingerichtet")
end

-- Währungs-Anzeige aktualisieren
function Components.UpdateCurrencyDisplay(currencies)
    local shopUI = playerGui:FindFirstChild("ShopUI")
    if not shopUI then return end

    local currencyFrame = shopUI.ShopFrame.CurrencyFrame

    -- Lösche alte Anzeigen
    for _, child in ipairs(currencyFrame:GetChildren()) do
        if child:IsA("TextLabel") then
            child:Destroy()
        end
    end

    -- Erstelle Währungs-Labels
    local currencyTypes = {"Coins", "Gems", "Tokens"}
    for i, currencyType in ipairs(currencyTypes) do
        local label = Instance.new("TextLabel")
        label.Name = currencyType .. "Label"
        label.Size = UDim2.new(0, 100, 1, 0)
        label.Position = UDim2.new(0, (i-1) * 105 + 10, 0, 0)
        label.BackgroundTransparency = 1
        label.Text = currencyType .. ": " .. (currencies[currencyType] or 0)
        label.TextColor3 = Color3.fromRGB(255, 255, 255)
        label.TextScaled = true
        label.Font = Enum.Font.SourceSansBold
        label.Parent = currencyFrame
    end
end

-- Permanente UI-Button-Leiste erstellen
function Components.CreatePermanentUIBar()
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "PermanentUIBar"
    screenGui.ResetOnSpawn = false
    screenGui.DisplayOrder = 100 -- Hohe Priorität
    screenGui.Parent = playerGui

    -- Hauptframe für die Button-Leiste
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "UIButtonBar"
    mainFrame.Size = UDim2.new(0, 300, 0, 60)
    mainFrame.Position = UDim2.new(0.5, -150, 1, -80) -- Unten zentriert
    mainFrame.BackgroundColor3 = Color3.fromRGB(25, 25, 25)
    mainFrame.BackgroundTransparency = 0.1
    mainFrame.BorderSizePixel = 0
    mainFrame.Parent = screenGui

    -- Abgerundete Ecken
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 12)
    corner.Parent = mainFrame

    -- Schatten-Effekt
    local shadow = Instance.new("Frame")
    shadow.Name = "Shadow"
    shadow.Size = UDim2.new(1, 4, 1, 4)
    shadow.Position = UDim2.new(0, -2, 0, -2)
    shadow.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    shadow.BackgroundTransparency = 0.7
    shadow.BorderSizePixel = 0
    shadow.ZIndex = mainFrame.ZIndex - 1
    shadow.Parent = mainFrame

    local shadowCorner = Instance.new("UICorner")
    shadowCorner.CornerRadius = UDim.new(0, 14)
    shadowCorner.Parent = shadow

    -- Button-Container
    local buttonContainer = Instance.new("Frame")
    buttonContainer.Size = UDim2.new(1, -10, 1, -10)
    buttonContainer.Position = UDim2.new(0, 5, 0, 5)
    buttonContainer.BackgroundTransparency = 1
    buttonContainer.Parent = mainFrame

    -- Layout für Buttons
    local layout = Instance.new("UIListLayout")
    layout.FillDirection = Enum.FillDirection.Horizontal
    layout.HorizontalAlignment = Enum.HorizontalAlignment.Center
    layout.VerticalAlignment = Enum.VerticalAlignment.Center
    layout.Padding = UDim.new(0, 8)
    layout.Parent = buttonContainer

    -- Inventar-Button
    local inventoryButton = Components.CreateUIButton("🎒", "Inventar", Color3.fromRGB(100, 150, 200))
    inventoryButton.Parent = buttonContainer
    inventoryButton.MouseButton1Click:Connect(function()
        print("🎒 Debug: Inventar-Button geklickt!")
        Components.ToggleInventory()
    end)

    -- Shop-Button
    local shopButton = Components.CreateUIButton("🛒", "Shop", Color3.fromRGB(150, 100, 200))
    shopButton.Parent = buttonContainer
    shopButton.MouseButton1Click:Connect(function()
        Components.ToggleShop()
    end)

    -- Quest-Button
    local questButton = Components.CreateUIButton("📋", "Quests", Color3.fromRGB(100, 200, 150))
    questButton.Parent = buttonContainer
    questButton.MouseButton1Click:Connect(function()
        Components.ToggleQuests()
    end)

    Logger.info("Permanente UI-Button-Leiste erstellt")
    return screenGui
end

-- UI-Button erstellen (Hilfsfunktion)
function Components.CreateUIButton(icon, text, color)
    local button = Instance.new("TextButton")
    button.Size = UDim2.new(0, 80, 1, 0)
    button.BackgroundColor3 = color
    button.BackgroundTransparency = 0.2
    button.BorderSizePixel = 0
    button.Text = icon .. "\n" .. text
    button.TextColor3 = Color3.fromRGB(255, 255, 255)
    button.TextScaled = true
    button.Font = Enum.Font.SourceSansBold

    -- Abgerundete Ecken
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = button

    -- Hover-Effekt
    button.MouseEnter:Connect(function()
        local tween = TweenService:Create(button, TweenInfo.new(0.2), {
            BackgroundTransparency = 0.1,
            Size = UDim2.new(0, 85, 1, 0)
        })
        tween:Play()
    end)

    button.MouseLeave:Connect(function()
        local tween = TweenService:Create(button, TweenInfo.new(0.2), {
            BackgroundTransparency = 0.2,
            Size = UDim2.new(0, 80, 1, 0)
        })
        tween:Play()
    end)

    return button
end

-- Quest-UI Toggle-Funktion hinzufügen
function Components.ToggleQuests()
    local questUI = playerGui:FindFirstChild("QuestUI")
    if questUI then
        questUI:Destroy()
        Logger.info("Quest-UI geschlossen")
    else
        Components.CreateQuestUI()
        Logger.info("Quest-UI geöffnet")
    end
end

-- Einfache Quest-UI erstellen (Platzhalter)
function Components.CreateQuestUI()
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "QuestUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui

    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "QuestFrame"
    mainFrame.Size = UDim2.new(0, 500, 0, 400)
    mainFrame.Position = UDim2.new(0.5, -250, 0.5, -200)
    mainFrame.BackgroundColor3 = Color3.fromRGB(30, 60, 30)
    mainFrame.BorderSizePixel = 0
    mainFrame.Parent = screenGui

    -- Abgerundete Ecken
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 12)
    corner.Parent = mainFrame

    -- Titel
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Size = UDim2.new(1, 0, 0, 50)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = "📋 QUESTS"
    titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = mainFrame

    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -35, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = mainFrame

    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 6)
    closeCorner.Parent = closeButton

    closeButton.MouseButton1Click:Connect(function()
        screenGui:Destroy()
    end)

    -- Quest-Inhalt (Platzhalter)
    local contentLabel = Instance.new("TextLabel")
    contentLabel.Size = UDim2.new(1, -20, 1, -70)
    contentLabel.Position = UDim2.new(0, 10, 0, 60)
    contentLabel.BackgroundTransparency = 1
    contentLabel.Text = "🎯 Aktive Quests:\n\n• Sammle 10 Holz (5/10)\n• Überlebe 5 Minuten (3/5)\n• Besuche den Shop (0/1)\n\n📋 Quest-System vollständig integriert!"
    contentLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    contentLabel.TextScaled = true
    contentLabel.Font = Enum.Font.SourceSans
    contentLabel.TextXAlignment = Enum.TextXAlignment.Left
    contentLabel.TextYAlignment = Enum.TextYAlignment.Top
    contentLabel.Parent = mainFrame
end

-- Komponenten initialisieren
function Components.Initialize()
    Components.SetupInputHandlers()
    Components.SetupInventoryEvents()
    Components.SetupShopEvents()
    Components.CreatePermanentUIBar() -- Neue UI-Leiste erstellen
    Logger.systemEvent("Components", "Initialized", "UI-Komponenten mit permanenter Button-Leiste bereit")
end

return Components
