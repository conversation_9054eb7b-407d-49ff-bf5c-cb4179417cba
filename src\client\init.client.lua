-- Client Ha<PERSON>tskript
-- Initialisiert alle Client-seitigen Services und Module

print("🎮 Client wird initialisiert...")

-- Services laden
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

-- Lokale Variablen
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Shared Module laden
local SharedModules = ReplicatedStorage:WaitForChild("Shared")
local Modules = ReplicatedStorage:WaitForChild("Modules")

-- Components laden (warten bis verfügbar)
local Components = ReplicatedStorage:WaitForChild("Components")
Components = require(Components)

-- Client Services initialisieren
local ClientServices = {
    Components = Components
}

-- Hauptinitialisierung
local function initialize()
    print("✅ Client erfolgreich initialisiert für Spieler:", player.Name)

    -- Client-Services initialisieren
    for serviceName, service in pairs(ClientServices) do
        if service.Initialize then
            service.Initialize()
            print("🔧 " .. serviceName .. " Client-Service initialisiert")
        end
    end

    print("🎮 Client bereit! Nutze die UI-Buttons unten für Inventar, Shop und Quests")
end

-- Initialisierung starten
initialize()
