-- Client Hauptskript
-- Initialisiert alle Client-seitigen Services und Module

print("🎮 Client wird initialisiert...")

-- Services laden
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

-- Lokale Variablen
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Shared Module laden
local SharedModules = ReplicatedStorage:WaitForChild("Shared")
local Modules = ReplicatedStorage:WaitForChild("Modules")

-- Components laden (warten bis verfügbar)
print("🔧 Debug: Lade Components von ReplicatedStorage...")
local Components = ReplicatedStorage:WaitForChild("Components")
print("🔧 Debug: Components-Modul gefunden:", Components ~= nil)
Components = require(Components)
print("🔧 Debug: Components erfolgreich geladen:", Components ~= nil)

-- Client Services initialisieren
local ClientServices = {
    Components = Components
}

-- Hauptinitialisierung
local function initialize()
    print("✅ Client erfolgreich initialisiert für Spieler:", player.Name)

    -- Client-Services initialisieren
    for serviceName, service in pairs(ClientServices) do
        print("🔧 Debug: Initialisiere Service:", serviceName)
        if service.Initialize then
            print("🔧 Debug: Service hat Initialize-Funktion")
            service.Initialize()
            print("🔧 " .. serviceName .. " Client-Service initialisiert")
        else
            print("⚠️ Debug: Service hat keine Initialize-Funktion:", serviceName)
        end
    end

    print("🎮 Client bereit! Nutze die UI-Buttons unten für Inventar, Shop und Quests")
end

-- Initialisierung starten
initialize()
