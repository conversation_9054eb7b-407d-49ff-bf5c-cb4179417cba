-- Advanced Map Generator V2 - Hochwertige Spielwelt ohne Spawn-Probleme
-- Komplett überarbeitete Version mit verbesserter Architektur

print("🗺️ Advanced Map Generator V2 wird gestartet...")

-- Services
local Workspace = game:GetService("Workspace")
local Lighting = game:GetService("Lighting")
local TweenService = game:GetService("TweenService")

-- Erweiterte Konfiguration
local CONFIG = {
    WORLD_SIZE = 400,
    GROUND_HEIGHT = 3,
    
    SPAWN = {
        PLATFORM_SIZE = Vector3.new(25, 1, 25),
        PLATFORM_POS = Vector3.new(0, 2, 0),
        SPAWN_HEIGHT = 4  -- Spawn ÜBER der Plattform
    },
    
    COLORS = {
        GROUND = Color3.fromRGB(76, 153, 0),
        SPAWN = Color3.fromRGB(65, 131, 215),
        SHOP = Color3.fromRGB(186, 85, 211),
        QUEST = Color3.fromRGB(50, 205, 50),
        FOREST = Color3.fromRGB(34, 139, 34),
        MINING = Color3.fromRGB(160, 82, 45),
        WATER = Color3.fromRGB(30, 144, 255),
        VILLAGE = Color3.fromRGB(255, 165, 0)
    }
}

-- Hilfsfunktionen
local function createPart(name, size, position, color, material, transparency, canCollide)
    local part = Instance.new("Part")
    part.Name = name
    part.Size = size
    part.Position = position
    part.Color = color
    part.Material = material or Enum.Material.Plastic
    part.Transparency = transparency or 0
    part.CanCollide = canCollide ~= false
    part.Anchored = true
    part.Parent = Workspace
    return part
end

local function createLabel(text, position, color)
    local label = createPart("Label_" .. text, Vector3.new(12, 1, 12), position, color, Enum.Material.Neon, 0.3)
    
    local gui = Instance.new("SurfaceGui")
    gui.Face = Enum.NormalId.Top
    gui.Parent = label
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = text
    textLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.TextStrokeTransparency = 0
    textLabel.Parent = gui
    
    return label
end

-- Terrain erstellen
local function createTerrain()
    print("🗺️ Erstelle Hauptterrain...")
    
    -- Hauptboden
    createPart("MainGround", 
              Vector3.new(CONFIG.WORLD_SIZE, CONFIG.GROUND_HEIGHT, CONFIG.WORLD_SIZE),
              Vector3.new(0, 0, 0),
              CONFIG.COLORS.GROUND,
              Enum.Material.Grass)
    
    print("✅ Hauptterrain erstellt")
end

-- KORRIGIERTER Spawn-Bereich
local function createSpawn()
    print("🗺️ Erstelle KORRIGIERTEN Spawn-Bereich...")
    
    -- Spawn-Plattform
    createPart("SpawnPlatform",
              CONFIG.SPAWN.PLATFORM_SIZE,
              CONFIG.SPAWN.PLATFORM_POS,
              CONFIG.COLORS.SPAWN,
              Enum.Material.Marble)
    
    -- Spawn-Point (RICHTIG positioniert - ÜBER der Plattform!)
    local spawn = Instance.new("SpawnLocation")
    spawn.Size = Vector3.new(4, 0.2, 4)  -- Sehr flach!
    spawn.Position = Vector3.new(0, CONFIG.SPAWN.SPAWN_HEIGHT, 0)  -- ÜBER der Plattform
    spawn.Transparency = 0.5
    spawn.BrickColor = BrickColor.new("Bright blue")
    spawn.Material = Enum.Material.ForceField
    spawn.Anchored = true
    spawn.Parent = Workspace
    
    -- Label
    createLabel("🏠 SPAWN", Vector3.new(0, 8, 0), CONFIG.COLORS.SPAWN)
    
    print("✅ KORRIGIERTER Spawn erstellt - kein Festsitzen mehr!")
end

-- Shop-Bereich
local function createShop()
    print("🗺️ Erstelle Shop-Bereich...")
    
    local pos = Vector3.new(-80, 3, -80)
    
    -- Plattform
    createPart("ShopPlatform", Vector3.new(30, 2, 30), pos, CONFIG.COLORS.SHOP, Enum.Material.Marble)
    
    -- Hauptgebäude
    createPart("ShopBuilding", Vector3.new(20, 15, 20), pos + Vector3.new(0, 8, 0), CONFIG.COLORS.SHOP, Enum.Material.Brick)
    
    -- Dach
    createPart("ShopRoof", Vector3.new(22, 3, 22), pos + Vector3.new(0, 16, 0), Color3.fromRGB(139, 69, 19), Enum.Material.Wood)
    
    -- Verkaufsstände
    for i = 1, 6 do
        local angle = (i - 1) * 60
        local x = pos.X + math.cos(math.rad(angle)) * 18
        local z = pos.Z + math.sin(math.rad(angle)) * 18
        createPart("Stand_" .. i, Vector3.new(3, 4, 3), Vector3.new(x, 5, z), Color3.fromRGB(139, 69, 19), Enum.Material.Wood)
    end
    
    createLabel("🛒 MARKTPLATZ", pos + Vector3.new(0, 20, 0), CONFIG.COLORS.SHOP)
    print("✅ Shop-Bereich erstellt")
end

-- Quest-Bereich
local function createQuest()
    print("🗺️ Erstelle Quest-Bereich...")
    
    local pos = Vector3.new(80, 3, -80)
    
    createPart("QuestPlatform", Vector3.new(30, 2, 30), pos, CONFIG.COLORS.QUEST, Enum.Material.Marble)
    createPart("QuestBuilding", Vector3.new(20, 15, 20), pos + Vector3.new(0, 8, 0), CONFIG.COLORS.QUEST, Enum.Material.Brick)
    createPart("QuestRoof", Vector3.new(22, 3, 22), pos + Vector3.new(0, 16, 0), Color3.fromRGB(139, 69, 19), Enum.Material.Wood)
    
    -- Quest-Tafeln
    for i = 1, 4 do
        local x = pos.X + (i - 2.5) * 6
        createPart("QuestBoard_" .. i, Vector3.new(4, 6, 1), Vector3.new(x, 6, pos.Z + 12), Color3.fromRGB(139, 69, 19), Enum.Material.Wood)
    end
    
    createLabel("📋 QUEST-HALLE", pos + Vector3.new(0, 20, 0), CONFIG.COLORS.QUEST)
    print("✅ Quest-Bereich erstellt")
end

-- Wald-Bereich
local function createForest()
    print("🗺️ Erstelle Wald-Bereich...")
    
    local centerPos = Vector3.new(-100, 2, 80)
    
    -- Waldboden
    createPart("ForestGround", Vector3.new(60, 1, 60), centerPos, CONFIG.COLORS.FOREST, Enum.Material.Grass)
    
    -- Bäume
    for i = 1, 20 do
        local x = centerPos.X + math.random(-25, 25)
        local z = centerPos.Z + math.random(-25, 25)
        local height = math.random(8, 15)
        
        -- Stamm
        createPart("TreeTrunk_" .. i, Vector3.new(2, height, 2), Vector3.new(x, centerPos.Y + height/2, z), 
                  Color3.fromRGB(101, 67, 33), Enum.Material.Wood)
        
        -- Krone
        local crown = createPart("TreeCrown_" .. i, Vector3.new(8, 8, 8), Vector3.new(x, centerPos.Y + height + 2, z), 
                                Color3.fromRGB(0, 128, 0), Enum.Material.Grass)
        crown.Shape = Enum.PartType.Ball
    end
    
    createLabel("🌲 MYSTISCHER WALD", centerPos + Vector3.new(0, 15, 0), CONFIG.COLORS.FOREST)
    print("✅ Wald-Bereich erstellt")
end

-- Mining-Bereich
local function createMining()
    print("🗺️ Erstelle Mining-Bereich...")
    
    local centerPos = Vector3.new(100, 2, 80)
    
    -- Mining-Boden
    createPart("MiningGround", Vector3.new(50, 1, 50), centerPos, CONFIG.COLORS.MINING, Enum.Material.Rock)
    
    -- Felsen und Erze
    for i = 1, 15 do
        local x = centerPos.X + math.random(-20, 20)
        local z = centerPos.Z + math.random(-20, 20)
        local size = math.random(3, 8)
        
        -- Felsen
        createPart("Rock_" .. i, Vector3.new(size, size, size), Vector3.new(x, centerPos.Y + size/2, z), 
                  Color3.fromRGB(105, 105, 105), Enum.Material.Rock)
        
        -- Erze (zufällig)
        if math.random() > 0.6 then
            local oreColors = {
                Color3.fromRGB(255, 215, 0),    -- Gold
                Color3.fromRGB(169, 169, 169),  -- Silber
                Color3.fromRGB(185, 242, 255)   -- Diamant
            }
            local oreColor = oreColors[math.random(1, #oreColors)]
            createPart("Ore_" .. i, Vector3.new(2, 2, 2), Vector3.new(x + 2, centerPos.Y + 2, z + 2), 
                      oreColor, Enum.Material.Neon)
        end
    end
    
    createLabel("⛏️ KRISTALL-MINE", centerPos + Vector3.new(0, 12, 0), CONFIG.COLORS.MINING)
    print("✅ Mining-Bereich erstellt")
end

-- Wasser-Bereich
local function createWater()
    print("🗺️ Erstelle Wasser-Bereich...")
    
    local pos = Vector3.new(0, -3, 120)
    
    -- Wasser
    local water = createPart("WaterArea", Vector3.new(80, 12, 80), pos, CONFIG.COLORS.WATER, Enum.Material.ForceField, 0.6, false)
    
    -- Kleine Inseln
    for i = 1, 3 do
        local x = pos.X + math.random(-30, 30)
        local z = pos.Z + math.random(-30, 30)
        createPart("Island_" .. i, Vector3.new(8, 2, 8), Vector3.new(x, 1, z), CONFIG.COLORS.GROUND, Enum.Material.Grass)
    end
    
    createLabel("🌊 KRISTALLSEE", Vector3.new(0, 8, 120), CONFIG.COLORS.WATER)
    print("✅ Wasser-Bereich erstellt")
end

-- Dorf-Bereich (NEU!)
local function createVillage()
    print("🗺️ Erstelle Dorf-Bereich...")
    
    local centerPos = Vector3.new(-120, 2, -20)
    
    -- Dorfplatz
    createPart("VillageSquare", Vector3.new(40, 1, 40), centerPos, CONFIG.COLORS.VILLAGE, Enum.Material.Concrete)
    
    -- Häuser
    local housePositions = {
        Vector3.new(-130, 5, -30), Vector3.new(-110, 5, -30),
        Vector3.new(-130, 5, -10), Vector3.new(-110, 5, -10),
        Vector3.new(-120, 5, 0)
    }
    
    for i, housePos in ipairs(housePositions) do
        createPart("House_" .. i, Vector3.new(8, 8, 8), housePos, Color3.fromRGB(178, 34, 34), Enum.Material.Brick)
        createPart("HouseRoof_" .. i, Vector3.new(10, 2, 10), housePos + Vector3.new(0, 5, 0), Color3.fromRGB(139, 69, 19), Enum.Material.Wood)
    end
    
    -- Brunnen in der Mitte
    createPart("WellBase", Vector3.new(4, 3, 4), centerPos + Vector3.new(0, 1.5, 0), Color3.fromRGB(105, 105, 105), Enum.Material.Concrete)
    createPart("WellWater", Vector3.new(2, 1, 2), centerPos + Vector3.new(0, 2.5, 0), CONFIG.COLORS.WATER, Enum.Material.ForceField, 0.5)
    
    createLabel("🏘️ FRIEDLICHES DORF", centerPos + Vector3.new(0, 12, 0), CONFIG.COLORS.VILLAGE)
    print("✅ Dorf-Bereich erstellt")
end

-- Beleuchtung
local function setupLighting()
    print("🗺️ Richte Beleuchtung ein...")
    
    Lighting.Brightness = 2.5
    Lighting.Ambient = Color3.fromRGB(120, 120, 120)
    Lighting.TimeOfDay = "14:00:00"
    Lighting.FogEnd = 2000
    
    -- Skybox
    local sky = Instance.new("Sky")
    sky.SkyboxBk = "rbxasset://textures/sky/sky512_bk.tex"
    sky.SkyboxDn = "rbxasset://textures/sky/sky512_dn.tex"
    sky.SkyboxFt = "rbxasset://textures/sky/sky512_ft.tex"
    sky.SkyboxLf = "rbxasset://textures/sky/sky512_lf.tex"
    sky.SkyboxRt = "rbxasset://textures/sky/sky512_rt.tex"
    sky.SkyboxUp = "rbxasset://textures/sky/sky512_up.tex"
    sky.Parent = Lighting
    
    print("✅ Beleuchtung eingerichtet")
end

-- Hauptfunktion
local function generateAdvancedMap()
    print("🗺️ Starte erweiterte Map-Generierung...")
    
    -- Lösche alte Map
    for _, obj in pairs(Workspace:GetChildren()) do
        if obj.Name:find("Area") or obj.Name:find("Platform") or obj.Name:find("Building") or 
           obj.Name:find("Tree") or obj.Name:find("Rock") or obj.Name:find("Ground") or
           obj.Name:find("Label") or obj.Name:find("House") or obj.Name:find("Stand") then
            obj:Destroy()
        end
    end
    
    -- Erstelle neue Map
    createTerrain()
    createSpawn()      -- KORRIGIERT!
    createShop()
    createQuest()
    createForest()
    createMining()
    createWater()
    createVillage()    -- NEU!
    setupLighting()
    
    print("✅ Erweiterte Map-Generierung abgeschlossen!")
    print("🎮 7 Bereiche erstellt:")
    print("   🏠 Spawn (Mitte) - KEIN FESTSITZEN MEHR!")
    print("   🛒 Marktplatz (Links)")
    print("   📋 Quest-Halle (Rechts)")
    print("   🌲 Mystischer Wald")
    print("   ⛏️ Kristall-Mine")
    print("   🌊 Kristallsee")
    print("   🏘️ Friedliches Dorf")
end

-- Map generieren
wait(3)
generateAdvancedMap()
print("🎮 Erweiterte Map ist bereit!")
