-- Map-Konfiguration
-- <PERSON><PERSON> können Sie alle Map-Einstellungen anpassen

local MapConfig = {}

-- Grundlegende Map-Einstellungen
MapConfig.WORLD = {
    SIZE = 200,              -- <PERSON><PERSON><PERSON><PERSON> der Hauptfläche
    GROUND_HEIGHT = 2,       -- <PERSON>öhe des Bodens
    SPAWN_HEIGHT = 5,        -- <PERSON><PERSON><PERSON> des Spawn-Punkts
}

-- Bereichsgrößen
MapConfig.AREAS = {
    SPAWN = {
        SIZE = Vector3.new(20, 3, 20),
        POSITION = Vector3.new(0, 3, 0),
        LABEL_POSITION = Vector3.new(0, 7, 0)
    },
    
    SHOP = {
        SIZE = Vector3.new(15, 2, 15),
        POSITION = Vector3.new(-40, 2, -40),
        LABEL_POSITION = Vector3.new(-40, 5, -40),
        BUILDING_SIZE = Vector3.new(12, 8, 12),
        BUILDING_POSITION = Vector3.new(-40, 6, -40)
    },
    
    QUEST = {
        SIZE = Vector3.new(15, 2, 15),
        POSITION = Vector3.new(40, 2, -40),
        LABEL_POSITION = Vector3.new(40, 5, -40),
        BUILDING_SIZE = Vector3.new(12, 8, 12),
        BUILDING_POSITION = Vector3.new(40, 6, -40)
    },
    
    FOREST = {
        SIZE = Vector3.new(30, 1, 30),
        POSITION = Vector3.new(-60, 1, 40),
        LABEL_POSITION = Vector3.new(-60, 3, 40),
        TREE_COUNT = 8
    },
    
    MINING = {
        SIZE = Vector3.new(25, 1, 25),
        POSITION = Vector3.new(60, 1, 40),
        LABEL_POSITION = Vector3.new(60, 3, 40),
        ROCK_COUNT = 6
    },
    
    WATER = {
        SIZE = Vector3.new(40, 8, 40),
        POSITION = Vector3.new(0, -2, 60),
        LABEL_POSITION = Vector3.new(0, 3, 60)
    }
}

-- Farb-Schema
MapConfig.COLORS = {
    -- Grundfarben
    GROUND = Color3.fromRGB(34, 139, 34),      -- Gras-Grün
    WATER = Color3.fromRGB(0, 100, 255),       -- Wasser-Blau
    
    -- Bereichsfarben
    SPAWN = Color3.fromRGB(100, 150, 200),     -- Spawn-Blau
    SHOP = Color3.fromRGB(150, 100, 200),      -- Shop-Lila
    QUEST = Color3.fromRGB(100, 200, 150),     -- Quest-Grün
    FOREST = Color3.fromRGB(0, 100, 0),        -- Wald-Dunkelgrün
    MINING = Color3.fromRGB(139, 69, 19),      -- Mine-Braun
    
    -- Objekt-Farben
    TREE_TRUNK = Color3.fromRGB(101, 67, 33),  -- Baumstamm-Braun
    TREE_LEAVES = Color3.fromRGB(0, 128, 0),   -- Blätter-Grün
    STONE = Color3.fromRGB(128, 128, 128),     -- Stein-Grau
    GOLD_ORE = Color3.fromRGB(255, 215, 0),    -- Gold-Gelb
    IRON_ORE = Color3.fromRGB(169, 169, 169),  -- Eisen-Grau
    DIAMOND_ORE = Color3.fromRGB(185, 242, 255) -- Diamant-Hellblau
}

-- Material-Einstellungen
MapConfig.MATERIALS = {
    GROUND = Enum.Material.Grass,
    SPAWN = Enum.Material.Neon,
    SHOP = Enum.Material.Marble,
    QUEST = Enum.Material.Marble,
    FOREST = Enum.Material.Grass,
    MINING = Enum.Material.Rock,
    WATER = Enum.Material.ForceField,
    BUILDING = Enum.Material.Brick,
    TREE_TRUNK = Enum.Material.Wood,
    TREE_LEAVES = Enum.Material.Grass,
    STONE = Enum.Material.Rock,
    ORE = Enum.Material.Neon
}

-- Beleuchtungs-Einstellungen
MapConfig.LIGHTING = {
    BRIGHTNESS = 2,
    AMBIENT = Color3.fromRGB(100, 100, 100),
    TIME_OF_DAY = "12:00:00",
    FOG_END = 1000,
    FOG_START = 0
}

-- Spawn-Einstellungen
MapConfig.SPAWN = {
    SAFE_ZONE_SIZE = Vector3.new(6, 1, 6),
    SAFE_ZONE_COLOR = BrickColor.new("Bright blue"),
    RESPAWN_TIME = 5
}

-- Objekt-Generierung
MapConfig.GENERATION = {
    TREES = {
        MIN_COUNT = 5,
        MAX_COUNT = 12,
        MIN_SIZE = Vector3.new(1.5, 6, 1.5),
        MAX_SIZE = Vector3.new(2.5, 10, 2.5),
        LEAVES_SIZE = Vector3.new(6, 6, 6)
    },
    
    ROCKS = {
        MIN_COUNT = 4,
        MAX_COUNT = 8,
        MIN_SIZE = Vector3.new(3, 3, 3),
        MAX_SIZE = Vector3.new(6, 6, 6)
    },
    
    ORES = {
        SPAWN_CHANCE = 0.6,  -- 60% Chance für Erz pro Felsen
        TYPES = {
            {NAME = "Gold", COLOR = "GOLD_ORE", CHANCE = 0.4},
            {NAME = "Iron", COLOR = "IRON_ORE", CHANCE = 0.4},
            {NAME = "Diamond", COLOR = "DIAMOND_ORE", CHANCE = 0.2}
        }
    }
}

-- Bereichs-Labels
MapConfig.LABELS = {
    SPAWN = "🏠 SPAWN",
    SHOP = "🛒 SHOP",
    QUEST = "📋 QUESTS",
    FOREST = "🌲 WALD",
    MINING = "⛏️ MINE",
    WATER = "🌊 WASSER"
}

-- Erweiterte Features
MapConfig.FEATURES = {
    ENABLE_SKYBOX = true,
    ENABLE_AMBIENT_SOUNDS = false,
    ENABLE_WEATHER = false,
    ENABLE_DAY_NIGHT_CYCLE = false,
    ENABLE_TELEPORTERS = false
}

-- Teleporter-Positionen (falls aktiviert)
MapConfig.TELEPORTERS = {
    {FROM = Vector3.new(0, 5, 0), TO = Vector3.new(-40, 5, -40), LABEL = "Zum Shop"},
    {FROM = Vector3.new(0, 5, 0), TO = Vector3.new(40, 5, -40), LABEL = "Zu Quests"},
    {FROM = Vector3.new(0, 5, 0), TO = Vector3.new(-60, 3, 40), LABEL = "Zum Wald"},
    {FROM = Vector3.new(0, 5, 0), TO = Vector3.new(60, 3, 40), LABEL = "Zur Mine"}
}

-- Validierungs-Funktionen
function MapConfig.ValidateConfig()
    -- Prüfe ob alle erforderlichen Werte gesetzt sind
    assert(MapConfig.WORLD.SIZE > 0, "World size must be positive")
    assert(MapConfig.AREAS.SPAWN.SIZE, "Spawn area size must be defined")
    
    print("✅ Map-Konfiguration validiert")
    return true
end

-- Hilfsfunktionen
function MapConfig.GetRandomTreeSize()
    local min = MapConfig.GENERATION.TREES.MIN_SIZE
    local max = MapConfig.GENERATION.TREES.MAX_SIZE
    
    return Vector3.new(
        math.random(min.X * 10, max.X * 10) / 10,
        math.random(min.Y * 10, max.Y * 10) / 10,
        math.random(min.Z * 10, max.Z * 10) / 10
    )
end

function MapConfig.GetRandomRockSize()
    local min = MapConfig.GENERATION.ROCKS.MIN_SIZE
    local max = MapConfig.GENERATION.ROCKS.MAX_SIZE
    
    return Vector3.new(
        math.random(min.X * 10, max.X * 10) / 10,
        math.random(min.Y * 10, max.Y * 10) / 10,
        math.random(min.Z * 10, max.Z * 10) / 10
    )
end

function MapConfig.GetRandomOreType()
    local rand = math.random()
    local cumulative = 0
    
    for _, ore in ipairs(MapConfig.GENERATION.ORES.TYPES) do
        cumulative = cumulative + ore.CHANCE
        if rand <= cumulative then
            return ore
        end
    end
    
    -- Fallback
    return MapConfig.GENERATION.ORES.TYPES[1]
end

return MapConfig
